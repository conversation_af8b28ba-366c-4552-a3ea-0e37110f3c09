package ru.portfolio.ax.rest.dto.ej;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO для ответа API ЭЖ метода rating/v1/rank/subjects
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EjRankSubjectsResponseDTO {
    
    /**
     * ID предмета
     */
    @JsonProperty("subjectId")
    private Long subjectId;
    
    /**
     * Название предмета
     */
    @JsonProperty("subjectName")
    private String subjectName;
    
    /**
     * Рейтинговые данные по предмету
     */
    @JsonProperty("rank")
    private SubjectRankData rank;
    
    /**
     * Данные рейтинга по предмету
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubjectRankData {
        
        /**
         * Средняя оценка по предмету по пятибалльной системе
         */
        @JsonProperty("averageMarkFive")
        private Double averageMarkFive;
        
        /**
         * Место в рейтинге по предмету
         */
        @JsonProperty("rankPlace")
        private Integer rankPlace;
        
        /**
         * Статус рейтинга по предмету
         */
        @JsonProperty("rankStatus")
        private String rankStatus;
    }
}
