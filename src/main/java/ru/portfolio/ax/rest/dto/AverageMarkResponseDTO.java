package ru.portfolio.ax.rest.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO для ответа метода получения средней оценки и рейтинга по предметам
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AverageMarkResponseDTO {
    
    /**
     * Общая средняя оценка по пятибалльной системе
     */
    @JsonProperty("averageMarkFive")
    private Double averageMarkFive;
    
    /**
     * Рейтинг по предметам
     */
    @JsonProperty("subjectsRank")
    private List<SubjectRankDTO> subjectsRank;
    
    /**
     * DTO для данных по предмету в рейтинге
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SubjectRankDTO {
        
        /**
         * ID предмета
         */
        @JsonProperty("subjectId")
        private Long subjectId;
        
        /**
         * Название предмета
         */
        @JsonProperty("subjectName")
        private String subjectName;
        
        /**
         * Средняя оценка по предмету по пятибалльной системе
         */
        @JsonProperty("averageMarkFiveSubject")
        private Double averageMarkFiveSubject;
    }
}
