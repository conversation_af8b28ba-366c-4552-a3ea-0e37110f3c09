package ru.portfolio.ax.rest.dto.ej;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO для ответа API ЭЖ метода rating/v1/rank/class
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EjRankClassResponseDTO {
    
    /**
     * ID идентификатор учащегося
     */
    @JsonProperty("personId")
    private String personId;
    
    /**
     * Рейтинговые данные
     */
    @JsonProperty("rank")
    private RankData rank;
    
    /**
     * Предыдущий рейтинг
     */
    @JsonProperty("previousRank")
    private PreviousRankData previousRank;
    
    /**
     * ID изображения
     */
    @JsonProperty("imageId")
    private Long imageId;
    
    /**
     * Данные рейтинга
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RankData {
        
        /**
         * Средняя оценка по пятибалльной системе
         */
        @JsonProperty("averageMarkFive")
        private Double averageMarkFive;
        
        /**
         * Место в рейтинге
         */
        @JsonProperty("rankPlace")
        private Integer rankPlace;
        
        /**
         * Статус рейтинга
         */
        @JsonProperty("rankStatus")
        private String rankStatus;
    }
    
    /**
     * Данные предыдущего рейтинга
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PreviousRankData {
        
        /**
         * Средняя оценка по пятибалльной системе
         */
        @JsonProperty("averageMarkFive")
        private Double averageMarkFive;
        
        /**
         * Место в рейтинге
         */
        @JsonProperty("rankPlace")
        private Integer rankPlace;
    }
}
