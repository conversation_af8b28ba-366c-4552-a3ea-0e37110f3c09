package ru.portfolio.ax.rest;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.configuration.datasource.aspect.WriteOnly;
import ru.portfolio.ax.model.*;
import ru.portfolio.ax.model.dto.*;
import ru.portfolio.ax.model.enums.PersonallyEntityEnum;
import ru.portfolio.ax.model.ref.MetaskillRef;
import ru.portfolio.ax.model.ref.SpoStatusRef;
import ru.portfolio.ax.rest.api.PortfolioApi;
import ru.portfolio.ax.rest.api.PositiveResponse;
import ru.portfolio.ax.rest.dto.*;
import ru.portfolio.ax.rest.dto.ej.EjRankClassResponseDTO;
import ru.portfolio.ax.rest.dto.ej.EjRankSubjectsResponseDTO;
import ru.portfolio.ax.rest.dto.dop.DPOProgram;
import ru.portfolio.ax.service.*;
import ru.portfolio.ax.service.diagnostic.independentRating.IndependentRatingService;
import ru.portfolio.ax.service.ext.DOPService;
import ru.portfolio.ax.service.ext.EjApiService;
import ru.portfolio.ax.service.ext.ProforientationService;
import ru.portfolio.ax.service.proforientation.recommendation.ProforientationRecommendationService;
import ru.portfolio.ax.service.gratitudeTeacher.GratitudeTeacherService;
import ru.portfolio.ax.service.giaExam.GiaExamService;
import ru.portfolio.ax.util.aspect.LoggedMethod;
import ru.portfolio.ax.util.logging.ActionHistory;
import ru.portfolio.ax.util.security.AuthComponent;
import ru.portfolio.ax.util.security.AuthComponentNew;
import ru.portfolio.ax.util.security.Secured;
import ru.portfolio.ax.util.security.SecuredNew;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.xml.bind.DatatypeConverter;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

import static org.springframework.http.HttpHeaders.CONTENT_DISPOSITION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/persons")
@Slf4j
public class PersonController {

    private final DataService service;
    private final AuthComponent authComponent;
    private final AuthComponentNew authComponentNew;
    private final ShareLinkService shareLinkService;
    private final ClickHouseService clickHouseService;
    private final PrintDocumentService printDocumentService;
    private final DOPService dopService;
    private final SettingService settingService;
    private final RecommendationService recommendationService;
    private final ProforientationRecommendationService proforientationRecommendationService;
    private final ProforientationService proforientationService;
    private final IndependentRatingService independentRatingService;
    private final GratitudeTeacherService gratitudeTeacherService;
    private final GiaExamService giaExamService;
    private final EjApiService ejApiService;

    @Value("${old.auth.enabled}")
    boolean oldAuthEnabled;

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @GetMapping("/info/{personId}")
    public PositiveResponse<PersonInfoDTO> getPersonInfoByGuid(@RequestHeader(name = "Authorization")
                                                               @Nullable String bearer,
                                                               @PathVariable String personId,
                                                               @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getPersonInfoByGuid", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.TEACHER, SecuredNew.GlobalRole.ADMIN_O_O)),
                    "getPersonInfoByGuid", personId);
        }
        return PortfolioApi.positiveResponse(service.getPersonInfo(personId, share, false));
    }

    @GetMapping("ids")
    @ApiOperation("Получение сязанных идентификаторов персоны")
    public PositiveResponse<PersonIdDTO> getPersonIds(@RequestParam String personId) {
        return PortfolioApi.positiveResponse(new PersonIdDTO(personId, clickHouseService.getPersonIds(personId)));
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @PostMapping("/{personId}/govexams")
    public PositiveResponse<List<ExamInfoDTO>> getExams(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                        @PathVariable String personId,
                                                        @Valid @RequestBody ExamDTO exam) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getExams", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getExams", personId);
        }
        return PortfolioApi.positiveResponse(clickHouseService.getExams(personId, exam));
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @GetMapping("/{personId}/govexams/list")
    public PositiveResponse<List<StandardExamDTO>> getExams(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                            @PathVariable String personId,
                                                            @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getExams", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getExams", personId);
        }
        return PortfolioApi.positiveResponse(giaExamService.getExamsByGuid(personId, share));
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @GetMapping("{personId}/academic/performance")
    public PositiveResponse<List<AcademicPerformanceBySchoolDTO>> progress(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                                           @PathVariable String personId,
                                                                           @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                                                                           @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
                                                                           @RequestParam(required = false) String stage,
                                                                           @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "progress", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "progress", personId);
        }
        return PortfolioApi.positiveResponse(clickHouseService.progress(personId, startDate, endDate, stage, share));
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @GetMapping("{personId}/academic-performance/final-mark")
    public PositiveResponse<List<MarkDTO>> finalMarkPerformance(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                                @PathVariable String personId,
                                                                @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getFinalMarks", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getFinalMarks", personId);
        }
        return PortfolioApi.positiveResponse(service.getFinalMarks(personId));
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @GetMapping("{personId}/academic-performance/average-by-theme")
    public PositiveResponse<AverageByThemeDTO> averageMarkByTheme(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                                  @PathVariable String personId,
                                                                  @RequestParam Long subjectId,
                                                                  @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getAverageMarkByTheme", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getAverageMarkByTheme", personId);
        }
        return PortfolioApi.positiveResponse(service.getAverageMarkByTheme(personId, subjectId));
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @GetMapping("{personId}/academic-performance/average-mark")
    public PositiveResponse<List<MarkDTO>> averageMarkPerformance(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                                  @PathVariable String personId,
                                                                  @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getAverageMarks", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getAverageMarks", personId);
        }
        return PortfolioApi.positiveResponse(service.getAverageMarks(personId));
    }

    @ReadOnly
    @LoggedMethod
    @GetMapping("{personId}/v2/academic-performance/average-mark")
    public PositiveResponse<AverageMarkResponseDTO> averageMarkPerformanceV2(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                                  @PathVariable String personId,
                                                                  @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getAverageMarks", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getAverageMarks", personId);
        }

        // Получаем данные рейтинга класса
        List<EjRankClassResponseDTO> rankClassData = ejApiService.getRankClass(personId, bearer);

        // Получаем данные рейтинга по предметам
        List<EjRankSubjectsResponseDTO> rankSubjectsData = ejApiService.getRankSubjects(personId, bearer);

        // Извлекаем общую среднюю оценку
        Double averageMarkFive = rankClassData.stream()
                .filter(data -> personId.equals(data.getPersonId()))
                .findFirst()
                .map(EjRankClassResponseDTO::getRank)
                .map(EjRankClassResponseDTO.RankData::getAverageMarkFive)
                .orElse(0.0);

        // Преобразуем данные по предметам
        List<AverageMarkResponseDTO.SubjectRankDTO> subjectsRank = rankSubjectsData.stream()
                .map(subject -> AverageMarkResponseDTO.SubjectRankDTO.builder()
                        .subjectId(subject.getSubjectId())
                        .subjectName(subject.getSubjectName())
                        .averageMarkFiveSubject(subject.getRank().getAverageMarkFive())
                        .build())
                .collect(java.util.stream.Collectors.toList());

        // Формируем ответ
        AverageMarkResponseDTO response = AverageMarkResponseDTO.builder()
                .averageMarkFive(averageMarkFive)
                .subjectsRank(subjectsRank)
                .build();

        return PortfolioApi.positiveResponse(response);
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @GetMapping("{personId}/academic/performance/average")
    public PositiveResponse<AverageAcademicPerformanceDTO> averagePerformance(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                                              @PathVariable String personId,
                                                                              @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "averagePerformance", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "averagePerformance", personId);
        }
        return PortfolioApi.positiveResponse(clickHouseService.getAveragePerformance(personId));
    }

    @WriteOnly
    //@Secured
    @LoggedMethod
    @PutMapping("/{personId}/olympiads/{olympiadId}")
    public PositiveResponse<OlympiadFullInfoDTO> olympiads(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                           @PathVariable String personId,
                                                           @PathVariable Long olympiadId,
                                                           @Validated @RequestBody NewOlympiadDTO newOlympiadDTO) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "olympiads", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "olympiads", personId);
        }

        return PortfolioApi.positiveResponse(clickHouseService.changeOlympiad(newOlympiadDTO, personId, olympiadId, bearer));
    }

    @WriteOnly
    //@Secured
    @LoggedMethod
    @ApiOperation("Метод добавления новой олимпиады")
    @PostMapping("/{personId}/olympiads")
    public PositiveResponse<OlympiadFullInfoDTO> olympiads(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                           @PathVariable String personId,
                                                           @Validated @RequestBody NewOlympiadDTO newOlympiadDTO) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "olympiads", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "olympiads", personId);
        }
        return PortfolioApi.positiveResponse(clickHouseService.addNewOlympiad(newOlympiadDTO, personId, bearer));
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @ApiOperation("Получение списка олимпиад Учащегося")
    @GetMapping("{personId}/olympiads/list")
    public PositiveResponse<List<OlympiadDTO>> olympiads(@RequestHeader(name = "Authorization", required = false) @Nullable String bearer,
                                                         @PathVariable String personId,
                                                         @ApiIgnore @CookieValue(value = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "olympiads", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "olympiads", personId);
        }
        return PortfolioApi.positiveResponse(clickHouseService.getOlympiads(personId, bearer, share));
    }

    @WriteOnly
    //@Secured
    @LoggedMethod
    @DeleteMapping("/{personId}/olympiads/{id}")
    public PositiveResponse<Object> olympiads(@RequestHeader(required = false) String authorization,
                                              @PathVariable String personId,
                                              @PathVariable Long id) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "olympiads", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "olympiads", personId);
        }
        clickHouseService.deleteOlympiad(id);
        return PortfolioApi.emptyPositiveResponse();
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @ApiOperation("Получение данных о выбранной Олимпиаде")
    @PostMapping("/olympiads")
    public PositiveResponse<OlympiadFullInfoDTO> olympiad(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                          @Valid @RequestBody OlympiadsDTO olympiadsDTO,
                                                          @ApiIgnore @CookieValue(value = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "olympiad", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "olympiad", null);
        }
        return PortfolioApi.positiveResponse(clickHouseService.getOlympiadInfo(bearer, olympiadsDTO, share));
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @ApiOperation("Получение данных о достижениях учащегося")
    @GetMapping("{personId}/achievements/list")
    public PositiveResponse<List<AchievementDTO>> achievements(@RequestHeader(name = "Authorization", required = false) String bearer,
                                                               @PathVariable String personId,
                                                               @ApiIgnore @CookieValue(value = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "achievements", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "achievements", personId);
        }
        return PortfolioApi.positiveResponse(clickHouseService.getAchievements(personId, bearer, share));
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @ApiOperation("Метод позволяет получить данные о выбранном достижении")
    @PostMapping("/achievements")
    public PositiveResponse<AchievementFullInfoDTO> achievements(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                                 @Valid @RequestBody AchievementsDTO achievementsDTO,
                                                                 @ApiIgnore @CookieValue(value = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "achievements", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "achievements", null);
        }
        return PortfolioApi.positiveResponse(clickHouseService.getAchievement(achievementsDTO, bearer, share));
    }

    @WriteOnly
    //@Secured
    @LoggedMethod
    @ApiOperation("Добавление нового достижения")
    @PostMapping("/{personId}/achievement")
    public PositiveResponse<AchievementDTO> achievements(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                         @PathVariable String personId,
                                                         @Validated @RequestBody NewAchievementDTO newAchievementDTO) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "achievements", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "achievements", personId);
        }
        return PortfolioApi.positiveResponse(clickHouseService.addNewAchievement(newAchievementDTO, personId, bearer));
    }

    @WriteOnly
    //@Secured
    @LoggedMethod
    @ApiOperation("Редактирование достижения учащегося")
    @PutMapping("/{personId}/achievement/{achievementId}")
    public PositiveResponse<AchievementDTO> achievements(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                         @PathVariable String personId,
                                                         @PathVariable Long achievementId,
                                                         @Validated @RequestBody NewAchievementDTO newAchievementDTO) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "achievements", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "achievements", personId);
        }
        return PortfolioApi.positiveResponse(clickHouseService.changeAchievement(newAchievementDTO, personId, achievementId, bearer));
    }

    @WriteOnly
    //@Secured
    @LoggedMethod
    @DeleteMapping("/{personId}/achievements/{id}")
    public PositiveResponse<Object> achievements(@RequestHeader(required = false) String authorization,
                                                 @PathVariable String personId,
                                                 @PathVariable Long id) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "achievements", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "achievements", personId);
        }
        clickHouseService.deleteAchievement(id, personId);
        return PortfolioApi.emptyPositiveResponse();
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @GetMapping("/{personId}/study/history")
    public PositiveResponse<List<HistoryDTO>> history(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                      @PathVariable String personId,
                                                      @ApiIgnore @CookieValue(value = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "history", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "history", personId);
        }
        return PortfolioApi.positiveResponse(service.getHistoryInfo(personId, share));
    }

    @ReadOnly
    //@Secured
    @LoggedMethod
    @GetMapping("{personId}/printpdf")
    public PositiveResponse<String> printPDF(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                             @PathVariable String personId,
                                             @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                                             @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "printPDF", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "printPDF", personId);
        }
        return PortfolioApi.positiveResponse(printDocumentService.createPortfolioPDF(personId, startDate, endDate, bearer));
    }

    //@Secured
    @PostMapping("{personId}/printPDF")
    public ResponseEntity<byte[]> printPDF(HttpServletRequest request,
                                           @Nullable String bearer,
                                           @PathVariable String personId,
                                           String fileName, String base64, String contentType) {
        String token = StringUtils.removeStart(StringUtils.removeStart(bearer, "Bearer+"), "Bearer ");
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), personId, "printPDF", null, token, request);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), personId, "printPDF", null, token, request);
        }
        return ResponseEntity.ok()
                .header(CONTENT_TYPE, contentType)
                .header(CONTENT_DISPOSITION, "attachment;filename=" + fileName)
                .body(DatatypeConverter.parseBase64Binary(base64));
    }

    @WriteOnly
    //@Secured
    @LoggedMethod
    @ActionHistory(kindCode = 4)
    @PostMapping("/{personId}/share")
    public PositiveResponse<ShareLinkUiDTO> createShareLink(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                            @PathVariable String personId,
                                                            @Valid @RequestBody ShareLinkDTO dto) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "createShareLink", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "createShareLink", personId);
        }
        return PortfolioApi.positiveResponse(shareLinkService.generateLink(bearer, personId, dto));
    }

    @ReadOnly
    //@Secured
    @LoggedMethod
    @GetMapping("/{personId}/share/list")
    public PositiveResponse<List<ShareLinkUiDTO>> getShareLinks(@RequestHeader(required = false) String authorization,
                                                                @PathVariable String personId) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getShareLinks", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getShareLinks", personId);
        }
        return PortfolioApi.positiveResponse(shareLinkService.getLinks(authorization, personId));
    }

    @WriteOnly
    //@Secured
    @LoggedMethod
    @PutMapping("/{personId}/share/activity")
    public PositiveResponse<Object> changeShareLinkActivity(@RequestHeader(required = false) String authorization,
                                                            @PathVariable String personId,
                                                            @Validated @RequestBody ShareLinkDTO.ActivityDTO dto) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "changeShareLinkActivity", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "changeShareLinkActivity", personId);
        }
        shareLinkService.changeActivity(personId, dto.getIsActive());
        return PortfolioApi.emptyPositiveResponse();
    }

    @WriteOnly
    //@Secured
    @LoggedMethod
    @DeleteMapping("/{personId}/share/{id}")
    public PositiveResponse<Object> deleteShareLinksByPerson(@RequestHeader(required = false) String authorization,
                                                             @PathVariable String personId,
                                                             @PathVariable Long id) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "deleteShareLinksByPerson", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "deleteShareLinksByPerson", personId);
        }
        shareLinkService.delete(id, personId);
        return PortfolioApi.emptyPositiveResponse();
    }

    @ReadOnly
    @LoggedMethod
    @PostMapping("share/linkSettings")
    public ResponseEntity<PositiveResponse<Object>> getCookie(@Validated @RequestBody ShareLinkDTO.UrlDTO dto) {
        Pair<String, String> pair = shareLinkService.generateCookie(dto.getUrl(), dto.getCurrentDate());
        ShareLinkUiDTO linkInfo = shareLinkService.getLinkInfo(dto);
        String cookie = "SHARE=" + pair.getSecond() + "; Path=/;";
        return ResponseEntity.ok()
                .header("Set-Cookie", cookie)
                .body(PortfolioApi.positiveResponse(linkInfo));
    }

    @ReadOnly
    @LoggedMethod
    //@Secured(urlCookie = true)
    @GetMapping("/{personId}/diagnostic")
    @ApiOperation("Метод позволяет получить данные о самодиагностике учащегося")
    public PositiveResponse<SelfDiagnosticWorkDTO> getDiagnostic(@RequestHeader(name = "Authorization", required = false) String authorization,
                                                                 @PathVariable("personId") String personId,
                                                                 @RequestParam("period") Integer period,
                                                                 @RequestParam(value = "subject", required = false) String subject,
                                                                 @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getDiagnostic", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getDiagnostic", personId);
        }
        return PortfolioApi.positiveResponse(clickHouseService.getSelfDiagnostic(authorization, personId, period, subject, share));
    }

    @ReadOnly
    @GetMapping("/{personId}/diagnostic/subjects")
    @ApiOperation("Получение списка предметов самодиагностик")
    public PositiveResponse<List<String>> getDiagnosticSubjects(@RequestHeader(required = false) String auth,
                                                                @PathVariable String personId,
                                                                @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        return PortfolioApi.positiveResponse(clickHouseService.getDiagnosticSubjects(auth, personId, share));
    }

    @ReadOnly
    //@Secured(urlCookie = true)
    @GetMapping("/{personId}/cultural/list")
    public PositiveResponse<List<CulturalInstitutionClickDTO>> getCultural(@PathVariable String personId,
                                                                           @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getCultural", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getCultural", personId);
        }
        return PortfolioApi.positiveResponse(
                clickHouseService.getVisitCulturalInstitutions(personId, share));
    }

    @ReadOnly
    //@Secured(urlCookie = true)
    @GetMapping("/{personId}/employments/list")
    public PositiveResponse<List<EmploymentDTO>> getEmployments(@PathVariable String personId,
                                                                @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getEmployments", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getEmployments", personId);
        }
        return PortfolioApi.positiveResponse(
                clickHouseService.getAchievementsFromClickhouse(personId, PersonallyEntityEnum.EMPLOYMENT.getCode(), share));
    }

    @ReadOnly
    //@Secured(urlCookie = true)
    @GetMapping("/{personId}/events/list")
    public PositiveResponse<List<EventDTO>> getEvents(@PathVariable String personId,
                                                      @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getEvents", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getEvents", personId);
        }
        return PortfolioApi.positiveResponse(
                clickHouseService.getAchievementsFromClickhouse(personId, PersonallyEntityEnum.EVENT.getCode(), share));
    }

    @ReadOnly
    //@Secured(urlCookie = true)
    @GetMapping("/{personId}/rewards/list")
    public PositiveResponse<List<RewardDTO>> getReward(@PathVariable String personId,
                                                       @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getReward", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getReward", personId);
        }
        return PortfolioApi.positiveResponse(
                clickHouseService.getAchievementsFromClickhouse(personId, PersonallyEntityEnum.REWARD.getCode(), share));
    }

    @ReadOnly
    //@Secured(urlCookie = true)
    @GetMapping("/{personId}/sport-rewards/list")
    public PositiveResponse<List<SportReward>> getSportRewards(@PathVariable String personId,
                                                               @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getSportRewards", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getSportRewards", personId);
        }
        return PortfolioApi.positiveResponse(
                clickHouseService.getAchievementsFromClickhouse(personId, PersonallyEntityEnum.SPORT_REWARD.getCode(), share));
    }

    @ReadOnly
    //@Secured(urlCookie = true)
    @GetMapping("/{personId}/affilations/list")
    public PositiveResponse<List<Affilation>> getAffilations(@PathVariable String personId,
                                                             @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getSportRewards", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getSportRewards", personId);
        }
        return PortfolioApi.positiveResponse(
                clickHouseService.getAchievementsFromClickhouse(personId, PersonallyEntityEnum.AFFILATION.getCode(), share));
    }

    @ReadOnly
    //  @Secured(urlCookie = true)
    @GetMapping("/{personId}/projects/list")
    public PositiveResponse<List<ProjectDTO>> getProjects(@PathVariable String personId,
                                                          @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getProjects", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getProjects", personId);
        }
        return PortfolioApi.positiveResponse(
                clickHouseService.getAchievementsFromClickhouse(personId, PersonallyEntityEnum.PROJECT.getCode(), share));
    }

    @ReadOnly
    //  @Secured(urlCookie = true)
    @GetMapping("/{personId}/gia-worldskills/list")
    public PositiveResponse<List<GIAWorldskills>> getGIAWorldskills(@PathVariable String personId,
                                                                    @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getGIAWorldskills", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getGIAWorldskills", personId);
        }
        return PortfolioApi.positiveResponse(
                clickHouseService.getAchievementsFromClickhouse(personId, PersonallyEntityEnum.GIA_WORLDSKILLS.getCode(), share));
    }

    @ReadOnly
    //  @Secured(urlCookie = true)
    @GetMapping("/{personId}/interests/list")
    public PositiveResponse<List<InterestDTO>> getInterests(@PathVariable String personId,
                                                            @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getInterests", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getInterests", personId);
        }
        return PortfolioApi.positiveResponse(
                service.getPersonInterests(personId, share));
    }

    @WriteOnly
    //@Secured
    @PostMapping("/{personId}/interests/list")
    public PositiveResponse<List<Interest>> getInterests(@PathVariable String personId,
                                                         @ApiIgnore @CookieValue(name = "SHARE", required = false) String share,
                                                         @RequestBody List<Interest> interests) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getInterests", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getInterests", personId);
        }
        return PortfolioApi.positiveResponse(
                service.saveInterestsList(interests));
    }

    @ReadOnly
    //  @Secured(byPerson = false)
    @GetMapping("/checkIn/organization/list")
    public PositiveResponse<NearbyOrganizationsDTO> getOrganizationsNearby(@RequestHeader(required = false) String authorization,
                                                                           @RequestParam("geocodeX") Double geocodeX,
                                                                           @RequestParam("geocodeY") Double geocodeY) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getOrganizationsNearby", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getOrganizationsNearby", null);
        }
        return PortfolioApi.positiveResponse(
                service.getNearbyOrganizationList(geocodeX, geocodeY));
    }

    @WriteOnly
    //@Secured
    @PostMapping("/{personId}/checkIn")
    public PositiveResponse<CheckInResponseDTO> addNewCheckIn(@PathVariable String personId,
                                                              @RequestHeader(required = false) String authorization,
                                                              @Valid @RequestBody NewCheckInDTO checkInDTO) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "addNewCheckIn", personId);
        } else {
            //todo добавить авторизацию
            //authComponentNew.userAuth(SecuredNew.Secure.standard(), "addNewCheckIn", personId);
        }
        return PortfolioApi.positiveResponse(service.addNewCheckIn(personId, checkInDTO));
    }

    @WriteOnly
    //  @Secured(byPerson = false)
    @PatchMapping("/{personId}/checkIn")
    public PositiveResponse<Object> deleteCheckInHistory(@RequestHeader(required = false) String authorization,
                                                         @PathVariable("personId") String personId,
                                                         @RequestBody Long id) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "deleteCheckInHistory", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "deleteCheckInHistory", personId);
        }
        service.deleteCheckInHistory(id);
        return PortfolioApi.emptyPositiveResponse();
    }


    //  @Secured(byPerson = false)
    @GetMapping("/{personId}/checkIn/list")
    public PositiveResponse<List<CheckInHistoryDTO>> getCheckInHistoryByPersonId
    (@RequestHeader(required = false) String authorization,
     @PathVariable("personId") String personId,
     @ApiIgnore @CookieValue(value = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getCheckInHistoryByPersonId", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getCheckInHistoryByPersonId", personId);
        }
        return PortfolioApi.positiveResponse(
                service.getCheckInHistoryByPersonId(personId, share));

    }

    //  @Secured(urlCookie = true)
    @GetMapping("/{personId}/independent-diagnostic")
    public PositiveResponse<GetIndependentDiagnosticsResponse> searchIndependentDiagnostic
    (@PathVariable("personId") String personId,
     @RequestParam Integer period,
     @RequestParam(value = "subject", required = false) String subject,
     @RequestParam Integer count,
     @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "searchIndependentDiagnostic", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchIndependentDiagnostic", personId);
        }
        return PortfolioApi.positiveResponse(
                clickHouseService.searchIndependentDiagnostic(personId, period, subject, share, count));
    }

    @GetMapping("/{personId}/personal-diagnostic-grouped")
    public PositiveResponse<List<GetPersonalDiagnosticResponse>> searchPersonalDiagnostic
            (@PathVariable("personId") String personId,
             @RequestParam(value = "subject", required = false) String subject,
             @RequestParam Integer count,
             @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "searchPersonalDiagnostic", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchPersonalDiagnostic", personId);
        }
        return PortfolioApi.positiveResponse(clickHouseService.searchPersonalDiagnostic(personId, subject, share, count));
    }

    //  @Secured(urlCookie = true)
    @GetMapping("/{personId}/independent-diagnostic-grouped")
    public PositiveResponse<List<GetIndependentDiagnosticsByLearningYearResponse>> searchIndependentDiagnostic
    (@PathVariable("personId") String personId,
     @RequestParam(value = "subject", required = false) String subject,
     @RequestParam Integer count,
     @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "searchIndependentDiagnostic", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchIndependentDiagnostic", personId);
        }
        return PortfolioApi.positiveResponse(clickHouseService.searchIndependentDiagnostic(personId, subject, share, count));
    }


    //  @Secured(byPerson = false)
    @GetMapping("/{personId}/function")
    public PositiveResponse<UserViewFunction> addUserViewedFunction
    (@RequestHeader(required = false) String authorization,
     @PathVariable("personId") String personId,
     @RequestParam("functionCode") Integer functionCode) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "addUserViewedFunction", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT)), "addUserViewedFunction", personId);
        }
        return PortfolioApi.positiveResponse(
                service.addUserViewedFunction(authorization, functionCode));

    }

    @ReadOnly
    //  @Secured(byPerson = false)
    @GetMapping("/{personId}/function/list")
    public PositiveResponse<List<ViewedFunction>> getUserViewedFunction(
            @RequestHeader(required = false) String authorization,
            @PathVariable("personId") String personId) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getUserViewedFunction", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT)), "getUserViewedFunction", personId);
        }
        return PortfolioApi.positiveResponse(service.getViewedFunctions(authorization));
    }


    @ReadOnly
    //  @Secured(byPerson = false)
    @GetMapping("/{personId}/result-lessons")
    public PositiveResponse<List<LessonsDTO>> getLessonsResults
            (@RequestHeader(required = false) String authorization,
             @PathVariable("personId") String personId,
             @RequestParam("subjectName") String subjectName) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getLessonsResults", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getLessonsResults", personId);
        }
        return PortfolioApi.positiveResponse(
                service.getLessonsResult(personId, subjectName));
    }


    @ReadOnly
    //  @Secured(urlCookie = true)
    @GetMapping("/{personId}/additional-education")
    public PositiveResponse<CertificatesDTO> getCertificates(@RequestHeader(required = false) String authorization,
                                                             @PathVariable("personId") String personId,
                                                             @ApiIgnore @CookieValue(name = "SHARE", required = false) String shareCookie) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getCertificates", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getCertificates", personId);
        }
        return PortfolioApi.positiveResponse(
                service.getCertificates(personId, authorization, shareCookie));
    }

    @WriteOnly
    //@Secured
    @PostMapping("/{personId}/error-message")
    public PositiveResponse<ErrorMessageDTO> getOrganizationsNearby(@RequestHeader(required = false) String authorization,
                                                                    @PathVariable("personId") String personId,
                                                                    @Valid @RequestBody ErrorMessageDTO errorMessageDTO) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getOrganizationsNearby", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT, SecuredNew.GlobalRole.AGENT)),
                    "getOrganizationsNearby", personId);
        }
        service.addErrorMessage(authorization, personId, errorMessageDTO);
        return PortfolioApi.positiveResponse(errorMessageDTO);
    }

    @ReadOnly
    //@Secured
    @GetMapping("/{personId}/error-message")
    public PositiveResponse<List<ErrorMessageDTO>> getErrorMessage(@RequestHeader(required = false) String
                                                                           authorization,
                                                                   @PathVariable("personId") String personId,
                                                                   @RequestParam(required = false) String entityType,
                                                                   @RequestParam(required = false) Long entityId,
                                                                   @RequestParam(required = false) String recordId) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getErrorMessage", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT, SecuredNew.GlobalRole.AGENT)),
                    "getErrorMessage", personId);
        }
        return PortfolioApi.positiveResponse(service.getErrorMessages(entityType, entityId, recordId));
    }

    @WriteOnly
    //@Secured
    @PutMapping("/{personId}/error-message")
    public PositiveResponse<ErrorMessageDTO> updateErrorMessage(@RequestHeader(required = false) String
                                                                        authorization,
                                                                @PathVariable("personId") String personId,
                                                                @Valid @RequestBody ErrorMessageDTO errorMessageDTO) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "updateErrorMessage", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT, SecuredNew.GlobalRole.AGENT)),
                    "updateErrorMessage", personId);
        }
        return PortfolioApi.positiveResponse(service.updateErrorMessages(authorization, personId, errorMessageDTO));
    }


    //  @Secured(byPerson = false)
    @PostMapping("/{personId}/recommendations/interest")
    public PositiveResponse<GetRecommendationsByInterestResponse> getRecommendationsByInterest
    (@RequestHeader(required = false) String authorization,
     @PathVariable("personId") String personId,
     @RequestBody GetRecommendationsByInterestRequest request) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getRecommendationsByInterest", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getRecommendationsByInterest", personId);
        }
        return PortfolioApi.positiveResponse(service.getRecommendationsByInterest(request, personId, authorization));
    }


    @ReadOnly

    //  @Secured(byPerson = false)
    @PostMapping("/{personId}/recommendations/classes")
    public PositiveResponse<List<ProffClassesRefDTO>> getRecommendClasses(@RequestHeader(required = false) String
                                                                                  authorization,
                                                                          @PathVariable("personId") String personId,
                                                                          @Valid @RequestBody GetRecommendClassesRequest request) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getRecommendClasses", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getRecommendClasses", personId);
        }
        return PortfolioApi.positiveResponse(service.getRecommendClasses(personId, request.getSchoolId(),
                request.getClassLevel(), request.getLimit()));
    }

    @GetMapping("/{personId}/avatar")
    public AvatarDTO getAvatar(@RequestHeader(name = "Authorization", required = false) String bearer,
                               @PathVariable("personId") String personId,
                               @ApiIgnore @CookieValue(value = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "saveAvatar", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                            SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.OPERATOR, SecuredNew.GlobalRole.ADMIN,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.TEACHER)),
                    "getAvatar", personId);
        }
        return settingService.getAvatar(bearer, share, personId);
    }


    @WriteOnly

    //  @Secured(byPerson = false)
    @ActionHistory(kindCode = 1)
    @PostMapping("/{personId}/gratitude-teacher")
    public PositiveResponse<Object> saveGratitudeTeacher(@RequestHeader(required = false) String authorization,
                                                         @PathVariable("personId") String personId,
                                                         @RequestBody GratitudeTeacher gratitudeTeacher) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "saveGratitudeTeacher", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT, SecuredNew.GlobalRole.AGENT)),
                    "saveGratitudeTeacher", personId);
        }
        gratitudeTeacherService.save(gratitudeTeacher);
        return PortfolioApi.emptyPositiveResponse();
    }

    @GetMapping("/{personId}/diagnostic/independent-rating")
    public PositiveResponse<List<IndependentRatingResponse>> getIndependentRating(@PathVariable("personId") String
                                                                                          personId) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "searchIndependentDiagnostic", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchIndependentDiagnostic", personId);
        }
        return PortfolioApi.positiveResponse(independentRatingService.getData(personId));
    }

    @GetMapping("/{personId}/diagnostic/general-rating")
    public PositiveResponse<List<GeneralRatingResponse>> getGeneralRating(@PathVariable("personId") String personId) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "searchGeneralIndependentDiagnostic", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "searchGeneralDiagnostic", personId);
        }
        return PortfolioApi.positiveResponse(clickHouseService.getGeneralRating(personId));
    }


    @ReadOnly

    //  @Secured(byPerson = false, urlCookie = true)
    @GetMapping("/{personId}/professional-education/list")
    public PositiveResponse<ProfessionalEducationResponse> getEducationList(@RequestHeader(required = false) String
                                                                                    authorization,
                                                                            @PathVariable("personId") String personId,
                                                                            @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookieNotByPerson(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getEducationList", personId);
        }
        return PortfolioApi.positiveResponse(service.getProfessionalEducation(authorization, personId, share));
    }


    @ReadOnly

    //  @Secured(byPerson = false, urlCookie = true)
    @GetMapping("/{personId}/professional-education/job")
    public PositiveResponse<ProfessionalEducationJobResponse> getEducationJob
            (@RequestHeader(required = false) String authorization,
             @PathVariable("personId") String personId,
             @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getEducationJob", personId);
        }
        return PortfolioApi.positiveResponse(service.getProfessionalEducationJob(authorization, personId, share));
    }


    @ReadOnly

    //  @Secured(byPerson = false, urlCookie = true)
    @GetMapping("/{personId}/professional-education/documents")
    public PositiveResponse<List<Document>> getProfessionalEducationDocuments
            (@RequestHeader(required = false) String authorization,
             @PathVariable("personId") String personId,
             @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getProfessionalEducationDocuments", personId);
        }
        return PortfolioApi.positiveResponse(service.getProfessionalEducationDocuments(authorization, personId, share));
    }


    @ReadOnly
    //  @Secured(byPerson = false, urlCookie = true)
    @GetMapping("/{personId}/professional-education/spo-status")
    public PositiveResponse<SpoStatusRef> getProfessionalEducationSpoStatus(@RequestHeader(required = false) String
                                                                                    authorization,
                                                                            @PathVariable("personId") String personId,
                                                                            @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {

        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getProfessionalEducationSpoStatus", personId);
        }
        return PortfolioApi.positiveResponse(service.getProfessionalEducationSpoStatus(authorization, personId, share));
    }


    @ReadOnly
    //  @Secured(byPerson = false)
    @GetMapping("/{personId}/professional-education/metaskills/list")
    public PositiveResponse<List<MetaskillRef>> getProfessionalEducationMetaskill
            (@RequestHeader(required = false) String authorization,
             @PathVariable("personId") String personId,
             @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "updateOrCreateMetaskill", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getProfessionalEducationMetaskill", personId);
        }
        return PortfolioApi.positiveResponse(service.getProfessionalEducationMetaskill(personId, share));
    }

    //  @Secured(byPerson = false)
    @PostMapping("/{personId}/professional-education/metaskill")
    public PositiveResponse<Object> updateOrCreateMetaskill(@RequestHeader(required = false) String authorization,
                                                            @PathVariable("personId") String personId,
                                                            @RequestBody Set<Integer> codes) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "updateOrCreateMetaskill", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "updateOrCreateMetaskill", personId);
        }
        service.updateMetaskill(personId, codes);
        return PortfolioApi.emptyPositiveResponse();
    }

    @WriteOnly
    @PostMapping("/{personId}/professional-education/spo-status")
    public PositiveResponse<Object> updateOrCreateSpoStatus(@RequestHeader(required = false) String authorization,
                                                            @PathVariable("personId") String personId,
                                                            @RequestBody Integer code) {
        service.updateSpoStatus(personId, code);
        return PortfolioApi.emptyPositiveResponse();
    }

    @WriteOnly
    @PostMapping("/{personId}/favorite-university")
    public PositiveResponse<Object> addFavoriteUniversity(@RequestHeader(required = false) String authorization,
                                                          @PathVariable("personId") String personId,
                                                          @RequestParam Integer universityId) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "addFavoriteUniversity", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.AGENT,
                    SecuredNew.GlobalRole.STUDENT)), "addFavoriteUniversity", personId);
        }

        service.addFavoriteUniversity(authorization, personId, universityId);
        return PortfolioApi.emptyPositiveResponse();
    }

    @ReadOnly
    @GetMapping("/{personId}/favorite-university")
    public PositiveResponse<List<FavoriteUniversity>> getFavoriteUniversities
            (@RequestHeader(required = false) String authorization,
             @PathVariable("personId") String personId) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getFavoriteUniversities", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.AGENT,
                    SecuredNew.GlobalRole.STUDENT, SecuredNew.GlobalRole.ADMIN)), "getFavoriteUniversities", personId);
        }
        return PortfolioApi.positiveResponse(service.getFavoriteUniversities(authorization, personId));
    }

    @WriteOnly
    @DeleteMapping("/{personId}/favorite-university")
    public PositiveResponse<Object> deleteFavoriteUniversities(@RequestHeader(required = false) String
                                                                       authorization,
                                                               @PathVariable("personId") String personId,
                                                               @RequestParam Integer universityId) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "deleteFavoriteUniversities", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.AGENT,
                    SecuredNew.GlobalRole.STUDENT)), "deleteFavoriteUniversities", personId);
        }
        service.deleteFavoriteUniversities(authorization, personId, universityId);
        return PortfolioApi.emptyPositiveResponse();
    }

    @ReadOnly
    @GetMapping("/{personId}/cards")
    public PositiveResponse<Cards> getAllCards(@RequestHeader(required = false) String authorization,
                                               @PathVariable("personId") String personId) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getAllCards", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.AGENT,
                    SecuredNew.GlobalRole.STUDENT, SecuredNew.GlobalRole.ADMIN)), "getAllCards", personId);
        }
        return PortfolioApi.positiveResponse(service.getAllCards(authorization, personId));
    }

    @ReadOnly
    @GetMapping("/{personId}/card")
    public PositiveResponse<CardDTO> getCard(@PathVariable("personId") String personId,
                                             @RequestParam String entityId,
                                             @RequestParam Integer entityType) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getAllCards", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.AGENT,
                    SecuredNew.GlobalRole.STUDENT, SecuredNew.GlobalRole.ADMIN)), "getAllCards", personId);
        }
        return PortfolioApi.positiveResponse(service.getCard(personId, entityType, entityId));
    }

    @WriteOnly
    @GetMapping("/{personId}/settings/personal-access")
    public PositiveResponse<PersonalDataAccess> getPersonalAccess(@RequestHeader(required = false) String authorization,
                                                                  @PathVariable("personId") String personId) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getPersonalAccess", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT)),
                    "getPersonalAccess", personId);
        }
        return PortfolioApi.positiveResponse(service.getPersonalAccess(authorization, personId));
    }

    @WriteOnly
    @PutMapping("/{personId}/settings/personal-access")
    public PositiveResponse<PersonalDataAccess> putPersonalAccess(@RequestHeader(required = false) String authorization,
                                                                  @PathVariable("personId") String personId,
                                                                  @RequestParam Boolean isActive) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getPersonalAccess", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.ADMIN)), "getPersonalAccess", personId);
        }
        return PortfolioApi.positiveResponse(service.changePersonalAccess(authorization, personId, isActive));
    }

    @ReadOnly
    @GetMapping("/{personId}/recommendations/mosreg")
    public PositiveResponse<List<DPOProgram>> getPODRecommendations(@RequestHeader(required = false) String authorization,
                                                                    @PathVariable("personId") String personId,
                                                                    @RequestParam Integer oktmo,
                                                                    @RequestParam Integer age) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getPODRecommendations", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)), "getPODRecommendations", personId);
        }
        return PortfolioApi.positiveResponse(dopService.getCurrentUserRoles(oktmo, age));
    }

    @ReadOnly
    @GetMapping("/{personId}/proforientation/recommendation/event")
    public PositiveResponse<GetRecommendationProforientationResponse> getRecommendationOnProforientation(
            @PathVariable String personId,
            @RequestParam String classLevel,
            @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookie(), "getRecommendationOnProforientation", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT)), "getRecommendationOnProforientation", personId);
        }
        return PortfolioApi.positiveResponse(proforientationRecommendationService.getData(personId, classLevel));
    }

    @ReadOnly
    @GetMapping("/{personId}/proforientation/recommendation/status")
    public PositiveResponse<GetRecommendStatusDTO> getRecommendationOnProforientation(
            @PathVariable String personId,
            @RequestParam @Nullable Integer statusPublication,
            @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getRecommendationStatus", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT)), "getRecommendationStatus", personId);
        }
        return PortfolioApi.positiveResponse(recommendationService.getRecommentationStatus(personId, statusPublication));
    }

    @ReadOnly
    @GetMapping("/{personId}/proforientation/getResults")
    public PositiveResponse<JsonNode> getProforientationResults(
            @PathVariable String personId,
            @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getRecommendationResults", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT)), "getRecommendationResults", personId);
        }
        JsonNode response = proforientationService.getRecommendations(personId).getBody();
        return PortfolioApi.positiveResponse(response.get("data"));
    }

    @ReadOnly
    @PostMapping("/{personId}/proforientation/report")
    public PositiveResponse<JsonNode> setProforientationResultsVisited(
            @PathVariable String personId,
            @ApiIgnore @CookieValue(name = "SHARE", required = false) String share) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), "getRecommendationResults", personId);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookie(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT)), "getRecommendationResults", personId);
        }
        JsonNode response = proforientationService.setReportVisited(personId).getBody();
        return PortfolioApi.positiveResponse(response.get("data"));
    }
}
