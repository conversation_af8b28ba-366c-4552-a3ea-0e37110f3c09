package ru.portfolio.ax.rest.admin;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import ru.portfolio.ax.rest.api.PortfolioApi;
import ru.portfolio.ax.rest.api.PositiveResponse;
import ru.portfolio.ax.service.CacheManagementService;
import ru.portfolio.ax.util.security.AuthComponent;
import ru.portfolio.ax.util.security.AuthComponentNew;
import ru.portfolio.ax.util.security.Secured;
import ru.portfolio.ax.util.security.SecuredNew;

import java.util.HashMap;
import java.util.Map;

/**
 * Административный контроллер для управления кешами
 * Доступен только администраторам
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/admin/cache")
public class CacheAdminController {

    private final CacheManagementService cacheManagementService;
    private final AuthComponent authComponent;
    private final AuthComponentNew authComponentNew;

    /**
     * Очистить все кеши API ЭЖ
     */
    @PostMapping("/ej/clear-all")
    public PositiveResponse<String> clearAllEjCaches() {
        // Проверяем права администратора
        authComponent.userAuth(Secured.Secure.notByPerson(), "clearAllEjCaches", null);
        
        cacheManagementService.evictAllEjCaches();
        log.info("Admin cleared all EJ caches");
        
        return PortfolioApi.positiveResponse("All EJ caches cleared successfully");
    }

    /**
     * Очистить кеши для конкретного учащегося
     */
    @PostMapping("/ej/clear-person/{personId}")
    public PositiveResponse<String> clearPersonCaches(@PathVariable String personId) {
        // Проверяем права администратора
        authComponent.userAuth(Secured.Secure.notByPerson(), "clearPersonCaches", null);
        
        cacheManagementService.evictAllCachesForPerson(personId);
        log.info("Admin cleared EJ caches for personId: {}", personId);
        
        return PortfolioApi.positiveResponse(
                String.format("EJ caches cleared for person: %s", personId));
    }

    /**
     * Получить статистику кешей
     */
    @GetMapping("/ej/stats")
    public PositiveResponse<Map<String, Object>> getCacheStats() {
        // Проверяем права администратора
        authComponent.userAuth(Secured.Secure.notByPerson(), "getCacheStats", null);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("rankClassCache", cacheManagementService.getRankClassCacheStats());
        stats.put("rankSubjectsCache", cacheManagementService.getRankSubjectsCacheStats());
        
        return PortfolioApi.positiveResponse(stats);
    }

    /**
     * Проверить, есть ли кешированные данные для учащегося
     */
    @GetMapping("/ej/check-person/{personId}")
    public PositiveResponse<Map<String, Object>> checkPersonCache(@PathVariable String personId) {
        // Проверяем права администратора
        authComponent.userAuth(Secured.Secure.notByPerson(), "checkPersonCache", null);
        
        boolean hasCachedData = cacheManagementService.hasCachedDataForPerson(personId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("personId", personId);
        result.put("hasCachedData", hasCachedData);
        
        return PortfolioApi.positiveResponse(result);
    }

    /**
     * Очистить только кеш рейтинга класса
     */
    @PostMapping("/ej/clear-rank-class")
    public PositiveResponse<String> clearRankClassCache() {
        authComponent.userAuth(Secured.Secure.notByPerson(), "clearRankClassCache", null);
        
        cacheManagementService.evictAllRankClassCache();
        log.info("Admin cleared rank class cache");
        
        return PortfolioApi.positiveResponse("Rank class cache cleared successfully");
    }

    /**
     * Очистить только кеш рейтинга по предметам
     */
    @PostMapping("/ej/clear-rank-subjects")
    public PositiveResponse<String> clearRankSubjectsCache() {
        authComponent.userAuth(Secured.Secure.notByPerson(), "clearRankSubjectsCache", null);
        
        cacheManagementService.evictAllRankSubjectsCache();
        log.info("Admin cleared rank subjects cache");
        
        return PortfolioApi.positiveResponse("Rank subjects cache cleared successfully");
    }
}
