package ru.portfolio.ax.rest.api.ej;

import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.DefaultResponseErrorHandler;
import ru.portfolio.ax.rest.exception.PortfolioException;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Error handler для API ЭЖ (Электронный журнал)
 */
public class EjServiceErrorHandler extends DefaultResponseErrorHandler {
    
    @Override
    public void handleError(ClientHttpResponse response) throws IOException {
        if (response.getStatusCode().is4xxClientError() || response.getStatusCode().is5xxServerError()) {
            int statusCode = response.getRawStatusCode();
            MediaType contentType = response.getHeaders().getContentType();
            if (Objects.nonNull(contentType) && errorEjMap.containsKey(statusCode)) {
                throw errorEjMap.get(statusCode);
            } else {
                throw new RuntimeException("EJ API error with status code: " + statusCode);
            }
        }
    }

    private final Map<Integer, PortfolioException> errorEjMap = new HashMap<Integer, PortfolioException>() {
        {
            put(401, PortfolioException.get420()); // Unauthorized
            put(403, PortfolioException.get420()); // Forbidden
            put(404, PortfolioException.get424()); // Not Found
            put(500, PortfolioException.get422()); // Internal Server Error
            put(502, PortfolioException.get422()); // Bad Gateway
            put(503, PortfolioException.get422()); // Service Unavailable
        }
    };
}
