package ru.portfolio.ax;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.*;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.TrustStrategy;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.http.HttpMethod;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RequestCallback;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import ru.portfolio.ax.repository.ClickHouseRepository;
import ru.portfolio.ax.rest.api.aupd.AupdServiceErrorHandler;
import ru.portfolio.ax.rest.api.ceds.CEDSServiceErrorHandler;
import ru.portfolio.ax.rest.api.contingent.ContingentServiceErrorHandler;
import ru.portfolio.ax.rest.api.dop.DOPServiceErrorHandler;
import ru.portfolio.ax.rest.api.ej.EjServiceErrorHandler;
import ru.portfolio.ax.rest.api.esz.ESZServiceErrorHandler;
import ru.portfolio.ax.rest.api.fos.FOSServiceErrorHandler;
import ru.portfolio.ax.rest.api.library.LibraryServiceErrorHandler;
import ru.portfolio.ax.rest.api.proforientation.ProforientationServiceErrorHandler;
import ru.portfolio.ax.rest.api.school.SchoolServiceErrorHandler;
import ru.portfolio.ax.rest.exception.PortfolioCodifiedEnum;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.util.ClickHouseInterceptor;
import ru.portfolio.ax.util.LoggingInterceptor;
import ru.portfolio.ax.util.URLParser;
import ru.portfolio.ax.util.Utils;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.lang.reflect.Field;
import java.net.SocketTimeoutException;
import java.net.URI;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;
import java.util.concurrent.Executor;

@Slf4j
@EnableAsync
@EnableCaching
@EnableScheduling
@EnableJpaAuditing
@SpringBootApplication
@RequiredArgsConstructor
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class PortfolioApplication {

    private final ClickHouseRepository clickHouseRepository;
    private final URLParser urlParser;
    private final ObjectMapper objectMapper;

    @Value("${external.rest-timeout.low}")
    private Integer restTimeoutTimeLow;
    @Value("${external.rest-read-timeout.low}")
    private Integer restReadTimeoutTimeLow;

    @Value("${external.rest-timeout}")
    private Integer restTimeoutTime;
    @Value("${external.rest-read-timeout}")
    private Integer restReadTimeoutTime;

    @Value("${external.rest-timeout.high}")
    private Integer restTimeoutTimeHigh;
    @Value("${external.rest-read-timeout.high}")
    private Integer restReadTimeoutTimeHigh;

    @Value("${clickhouse.logs.enabled}")
    private Boolean clickhouseLogEnabled;

    @Value("${logging.interceptor.enabled}")
    private Boolean loggingInterceptor;

    @Value("${http.max-per-route}")
    private Integer httpMaxPerRoute;

    @Value("${http.max-total}")
    private Integer httpMaxTotal;

    @Value("${dop.tls}")
    private Boolean eisDopTls;


    public static void main(String[] args) {
        SpringApplication.run(PortfolioApplication.class, args);
    }

    @Bean
    @Primary
    public Executor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("PortfolioWorkerThread-");
        executor.setQueueCapacity(500);
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.initialize();
        return new SimpleAsyncTaskExecutor(executor);
    }

    @Bean("contingentRestTemplate")
    public RestTemplate contingentRestTemplate() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        RestTemplate restTemplate = getRestTemplate(restTimeoutTimeLow, restReadTimeoutTimeLow);
        restTemplate.setErrorHandler(new ContingentServiceErrorHandler());
        return restTemplate;
    }

    @Bean("nsiRestTemplate")
    public RestTemplate nsiRestTemplate() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        return getRestTemplate(restTimeoutTime, restReadTimeoutTime);
    }

    @Bean("nsiExcelRestTemplate")
    public RestTemplate nsiExcelRestTemplate() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        return getRestTemplate(restTimeoutTime * 5, restReadTimeoutTime * 5);
    }

    @Bean("eszRestTemplate")
    public RestTemplate eszRestTemplate() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        RestTemplate restTemplate = getRestTemplate(restTimeoutTimeHigh, restReadTimeoutTimeHigh);
        restTemplate.setErrorHandler(new ESZServiceErrorHandler());
        return restTemplate;
    }

    @Bean("libraryRestTemplate")
    public RestTemplate libraryRestTemplate() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        RestTemplate restTemplate = getRestTemplate(restTimeoutTimeHigh, restReadTimeoutTimeHigh);
        restTemplate.setErrorHandler(new LibraryServiceErrorHandler());
        return restTemplate;
    }

    @Bean("aupdRestTemplate")
    public RestTemplate aupdRestTemplate() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        RestTemplate restTemplate = getRestTemplate(restTimeoutTimeLow, restReadTimeoutTimeLow);
        restTemplate.setErrorHandler(new AupdServiceErrorHandler());
        return restTemplate;
    }

    @Bean("dopRestTemplate")
    public RestTemplate dopRestTemplate() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        RestTemplate restTemplate = getDopRestTemplate(restTimeoutTimeHigh, restReadTimeoutTimeHigh);
        if (eisDopTls) {
            restTemplate = getDopRestTemplates(restTimeoutTimeHigh, restReadTimeoutTimeHigh);
        }
        restTemplate.setErrorHandler(new DOPServiceErrorHandler());
        return restTemplate;
    }

    @Bean("fosRestTemplate")
    public RestTemplate fosRestTemplate() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        RestTemplate restTemplate = getRestTemplate(restTimeoutTimeHigh, restReadTimeoutTimeHigh);
        restTemplate.setErrorHandler(new FOSServiceErrorHandler());
        return restTemplate;
    }

    @Bean("cedsRestTemplate")
    public RestTemplate cedsRestTemplate() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        RestTemplate restTemplate = getRestTemplate(restTimeoutTimeHigh, restReadTimeoutTimeHigh);
        restTemplate.setErrorHandler(new CEDSServiceErrorHandler());
        return restTemplate;
    }

    @Bean("schoolRestTemplate")
    public RestTemplate schoolRestTemplate() {
        RestTemplate restTemplate = getRestTemplateWithoutClickhouseInterceptor(restTimeoutTimeHigh, restReadTimeoutTimeHigh, urlParser, loggingInterceptor);
        restTemplate.setErrorHandler(new SchoolServiceErrorHandler());
        return restTemplate;
    }

    @Bean("proforientationRestTemplate")
    public RestTemplate proforientationRestTemplate() {
        RestTemplate restTemplate = getRestTemplateWithoutClickhouseInterceptor(restTimeoutTime, restReadTimeoutTime, urlParser, loggingInterceptor);
        restTemplate.setErrorHandler(new ProforientationServiceErrorHandler());
        return restTemplate;
    }

    @Bean("ejRestTemplate")
    public RestTemplate ejRestTemplate() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        RestTemplate restTemplate = getRestTemplate(restTimeoutTime, restReadTimeoutTime);
        restTemplate.setErrorHandler(new EjServiceErrorHandler());
        return restTemplate;
    }

    private RestTemplate getRestTemplate(Integer restTimeout, Integer restReadTimeout) throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);

        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(httpMaxTotal);
        connectionManager.setDefaultMaxPerRoute(httpMaxPerRoute);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setSSLSocketFactory(csf)
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);
        requestFactory.setConnectTimeout(restTimeout);
        requestFactory.setReadTimeout(restReadTimeout);
        RestTemplate restTemplate = new RestTemplate(requestFactory) {
            @Override
            public <T> T execute(URI url, HttpMethod method, RequestCallback requestCallback, ResponseExtractor<T> responseExtractor) throws RestClientException {
                try {
                    return super.execute(url, method, requestCallback, responseExtractor);
                } catch (RestClientException e) {
                    if (Objects.nonNull(e.getCause())
                            && (ConnectTimeoutException.class.isAssignableFrom(e.getCause().getClass()) ||
                            SocketTimeoutException.class.isAssignableFrom(e.getCause().getClass()))) {
                        String serviceNameByURL = urlParser.getServiceNameByURL(url.toString());
                        if (serviceNameByURL.equals("NSI")) {
                            try {
                                Field field = requestCallback.getClass().getDeclaredField("requestEntity");
                                boolean accessible = field.isAccessible();
                                field.setAccessible(true);
                                Object entity = field.get(requestCallback);
                                Field body = entity.getClass().getDeclaredField("body");
                                body.setAccessible(true);
                                serviceNameByURL = serviceNameByURL.concat(": ")
                                        .concat(objectMapper.writeValueAsString(body.get(entity)));
                                body.setAccessible(false);
                                field.setAccessible(accessible);
                            } catch (Exception ex) {
                            }
                        }
                        throw PortfolioException.getValidError(
                                new PortfolioException(PortfolioCodifiedEnum.E488, serviceNameByURL));
                    } else {
                        throw PortfolioException.getValidError(
                                new PortfolioException(PortfolioCodifiedEnum.E438, urlParser.getServiceNameByURL(url.toString())));
                    }
                }
            }
        };
        if (clickhouseLogEnabled) {
            restTemplate.setInterceptors(Arrays.asList(new LoggingInterceptor(), new ClickHouseInterceptor(clickhouseLogEnabled, clickHouseRepository)));
        } else {
            restTemplate.setInterceptors(Collections.singletonList(new LoggingInterceptor()));
        }

        return restTemplate;
    }

    private RestTemplate getDopRestTemplate(Integer restTimeout, Integer restReadTimeout) throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        TrustStrategy acceptingTrustStrategy = (x509Certificates, s) -> true;

        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();
        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier());

        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(httpMaxTotal);
        connectionManager.setDefaultMaxPerRoute(httpMaxPerRoute);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setSSLSocketFactory(csf).build();

        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);
        requestFactory.setConnectTimeout(restTimeout);
        requestFactory.setReadTimeout(restReadTimeout);

        RestTemplate restTemplate = new RestTemplate(requestFactory) {
            @Override
            public <T> T execute(URI url, HttpMethod method, RequestCallback requestCallback, ResponseExtractor<T> responseExtractor) throws RestClientException {
                try {
                    return super.execute(url, method, requestCallback, responseExtractor);
                } catch (RestClientException e) {
                    if (Objects.nonNull(e.getCause())
                            && (ConnectTimeoutException.class.isAssignableFrom(e.getCause().getClass()) ||
                            SocketTimeoutException.class.isAssignableFrom(e.getCause().getClass()))) {
                        throw PortfolioException.getValidError(
                                new PortfolioException(PortfolioCodifiedEnum.E488, "EIS DOP"));
                    } else {
                        String[] subarray = ArrayUtils.subarray(ExceptionUtils.getStackFrames(e), 0, 20);
                        log.info("Ошибка обращения к сервису EIS DOP \n {}", Utils.join(Arrays.asList(subarray)));
                        throw PortfolioException.getValidError(
                                new PortfolioException(PortfolioCodifiedEnum.E438, "EIS DOP"));
                    }
                }
            }
        };
        if (clickhouseLogEnabled) {
            restTemplate.setInterceptors(Arrays.asList(new LoggingInterceptor(), new ClickHouseInterceptor(clickhouseLogEnabled, clickHouseRepository)));
        } else {
            restTemplate.setInterceptors(Collections.singletonList(new LoggingInterceptor()));
        }

        return restTemplate;
    }

    public RestTemplate getDopRestTemplates(Integer restTimeout, Integer restReadTimeout) throws NoSuchAlgorithmException, KeyManagementException {
        TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
            public X509Certificate[] getAcceptedIssuers() {
                return null;
            }

            public void checkClientTrusted(X509Certificate[] certs, String authType) {
            }

            public void checkServerTrusted(X509Certificate[] certs, String authType) {
            }
        }};

        SSLContext sc = SSLContext.getInstance("TLS");
        sc.init(null, trustAllCerts, new SecureRandom());
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

        SSLConnectionSocketFactory sslConnectionSocketFactory = new SSLConnectionSocketFactory(Objects.requireNonNull(sc),
                NoopHostnameVerifier.INSTANCE);

        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", new PlainConnectionSocketFactory())
                .register("https", sslConnectionSocketFactory)
                .build();

        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
        connectionManager.setMaxTotal(httpMaxTotal);
        connectionManager.setDefaultMaxPerRoute(httpMaxPerRoute);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setSSLSocketFactory(sslConnectionSocketFactory).build();

        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);
        requestFactory.setConnectTimeout(restTimeout);
        requestFactory.setReadTimeout(restReadTimeout);

        RestTemplate restTemplate = new RestTemplate(requestFactory) {
            @Override
            public <T> T execute(URI url, HttpMethod method, RequestCallback requestCallback, ResponseExtractor<T> responseExtractor) throws RestClientException {
                try {
                    return super.execute(url, method, requestCallback, responseExtractor);
                } catch (RestClientException e) {
                    if (Objects.nonNull(e.getCause())
                            && (ConnectTimeoutException.class.isAssignableFrom(e.getCause().getClass()) ||
                            SocketTimeoutException.class.isAssignableFrom(e.getCause().getClass()))) {
                        throw PortfolioException.getValidError(
                                new PortfolioException(PortfolioCodifiedEnum.E488, "EIS DOP"));
                    } else {
                        String[] subarray = ArrayUtils.subarray(ExceptionUtils.getStackFrames(e), 0, 20);
                        log.info("Ошибка обращения к сервису EIS DOP {}", Utils.join(Arrays.asList(subarray)));
                        throw PortfolioException.getValidError(
                                new PortfolioException(PortfolioCodifiedEnum.E438, "EIS DOP"));
                    }
                }
            }
        };
        if (clickhouseLogEnabled) {
            restTemplate.setInterceptors(Arrays.asList(new LoggingInterceptor(), new ClickHouseInterceptor(clickhouseLogEnabled, clickHouseRepository)));
        } else {
            restTemplate.setInterceptors(Collections.singletonList(new LoggingInterceptor()));
        }

        return restTemplate;

    }

    @SneakyThrows
    public static RestTemplate getRestTemplateWithoutClickhouseInterceptor(Integer restTimeout, Integer restReadTimeout, URLParser urlParser, Boolean interceptorEnabled) {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(csf)
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setHttpClient(httpClient);
        requestFactory.setConnectTimeout(restTimeout);
        requestFactory.setReadTimeout(restReadTimeout);
        RestTemplate restTemplate = new RestTemplate(requestFactory) {
            @Override
            public <T> T execute(URI url, HttpMethod method, RequestCallback requestCallback, ResponseExtractor<T> responseExtractor) throws RestClientException {
                try {
                    return super.execute(url, method, requestCallback, responseExtractor);
                } catch (RestClientException e) {
                    if (Objects.nonNull(e.getCause())
                            && (ConnectTimeoutException.class.isAssignableFrom(e.getCause().getClass()) ||
                            SocketTimeoutException.class.isAssignableFrom(e.getCause().getClass()))) {
                        throw PortfolioException.getValidError(
                                new PortfolioException(PortfolioCodifiedEnum.E488, urlParser.getServiceNameByURL(url.toString())));
                    } else {
                        throw PortfolioException.getValidError(
                                new PortfolioException(PortfolioCodifiedEnum.E438, urlParser.getServiceNameByURL(url.toString())));
                    }

                }
            }
        };
        if (interceptorEnabled) {
            restTemplate.setInterceptors(Arrays.asList(new LoggingInterceptor()));
        }

        return restTemplate;
    }
}
