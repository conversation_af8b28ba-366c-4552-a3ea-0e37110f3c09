package ru.portfolio.ax.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;
import ru.portfolio.ax.configuration.CacheConfiguration;

import java.util.Objects;

/**
 * Сервис для управления кешами
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheManagementService {

    private final CacheManager cacheManager;

    /**
     * Очистить кеш рейтинга класса для конкретного учащегося
     * 
     * @param personId ID учащегося
     */
    public void evictRankClassCache(String personId) {
        Cache cache = cacheManager.getCache(CacheConfiguration.EJ_RANK_CLASS_CACHE);
        if (cache != null) {
            cache.evict(personId);
            log.info("Evicted rank class cache for personId: {}", personId);
        }
    }

    /**
     * Очистить кеш рейтинга по предметам для конкретного учащегося
     * 
     * @param personId ID учащегося
     */
    public void evictRankSubjectsCache(String personId) {
        Cache cache = cacheManager.getCache(CacheConfiguration.EJ_RANK_SUBJECTS_CACHE);
        if (cache != null) {
            cache.evict(personId);
            log.info("Evicted rank subjects cache for personId: {}", personId);
        }
    }

    /**
     * Очистить все кеши для конкретного учащегося
     * 
     * @param personId ID учащегося
     */
    public void evictAllCachesForPerson(String personId) {
        evictRankClassCache(personId);
        evictRankSubjectsCache(personId);
        log.info("Evicted all EJ caches for personId: {}", personId);
    }

    /**
     * Очистить весь кеш рейтинга класса
     */
    public void evictAllRankClassCache() {
        Cache cache = cacheManager.getCache(CacheConfiguration.EJ_RANK_CLASS_CACHE);
        if (cache != null) {
            cache.clear();
            log.info("Cleared all rank class cache");
        }
    }

    /**
     * Очистить весь кеш рейтинга по предметам
     */
    public void evictAllRankSubjectsCache() {
        Cache cache = cacheManager.getCache(CacheConfiguration.EJ_RANK_SUBJECTS_CACHE);
        if (cache != null) {
            cache.clear();
            log.info("Cleared all rank subjects cache");
        }
    }

    /**
     * Очистить все кеши API ЭЖ
     */
    public void evictAllEjCaches() {
        evictAllRankClassCache();
        evictAllRankSubjectsCache();
        log.info("Cleared all EJ caches");
    }

    /**
     * Получить статистику кеша рейтинга класса
     * 
     * @return информация о кеше
     */
    public String getRankClassCacheStats() {
        Cache cache = cacheManager.getCache(CacheConfiguration.EJ_RANK_CLASS_CACHE);
        if (cache != null) {
            return String.format("Cache '%s': %s", 
                    CacheConfiguration.EJ_RANK_CLASS_CACHE, 
                    cache.getNativeCache().toString());
        }
        return "Cache not found";
    }

    /**
     * Получить статистику кеша рейтинга по предметам
     * 
     * @return информация о кеше
     */
    public String getRankSubjectsCacheStats() {
        Cache cache = cacheManager.getCache(CacheConfiguration.EJ_RANK_SUBJECTS_CACHE);
        if (cache != null) {
            return String.format("Cache '%s': %s", 
                    CacheConfiguration.EJ_RANK_SUBJECTS_CACHE, 
                    cache.getNativeCache().toString());
        }
        return "Cache not found";
    }

    /**
     * Проверить, есть ли данные в кеше для конкретного учащегося
     * 
     * @param personId ID учащегося
     * @return true, если данные есть в любом из кешей
     */
    public boolean hasCachedDataForPerson(String personId) {
        Cache rankClassCache = cacheManager.getCache(CacheConfiguration.EJ_RANK_CLASS_CACHE);
        Cache rankSubjectsCache = cacheManager.getCache(CacheConfiguration.EJ_RANK_SUBJECTS_CACHE);
        
        boolean hasRankClass = rankClassCache != null && rankClassCache.get(personId) != null;
        boolean hasRankSubjects = rankSubjectsCache != null && rankSubjectsCache.get(personId) != null;
        
        return hasRankClass || hasRankSubjects;
    }
}
