package ru.portfolio.ax.service.ext;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import ru.portfolio.ax.configuration.EjProperty;
import ru.portfolio.ax.rest.dto.ej.EjRankClassResponseDTO;
import ru.portfolio.ax.rest.dto.ej.EjRankSubjectsResponseDTO;
import ru.portfolio.ax.util.RestUtils;

import java.util.List;
import java.util.Objects;

/**
 * Сервис для работы с API ЭЖ (Электронный журнал)
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EjApiService {
    
    private final EjProperty ejProperty;
    private final RestTemplate ejRestTemplate;
    
    /**
     * Получить рейтинг по общему среднему баллу для учащегося
     * 
     * @param personId ID учащегося
     * @param authorization токен авторизации
     * @return список данных рейтинга
     */
    public List<EjRankClassResponseDTO> getRankClass(String personId, String authorization) {
        log.debug("Getting rank class for personId: {}", personId);
        
        String url = ejProperty.getUrl() + ejProperty.getRankClassEndpoint();
        
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam("personId", personId);
        
        ResponseEntity<List<EjRankClassResponseDTO>> response = ejRestTemplate.exchange(
                builder.build().toUri(),
                HttpMethod.GET,
                RestUtils.createSimpleHeaserEntity(null),
                new ParameterizedTypeReference<List<EjRankClassResponseDTO>>() {}
        );
        
        List<EjRankClassResponseDTO> result = Objects.requireNonNull(response.getBody());
        log.debug("Retrieved {} rank class records for personId: {}", result.size(), personId);
        
        return result;
    }
    
    /**
     * Получить рейтинг по предметам для учащегося
     * 
     * @param personId ID учащегося
     * @param authorization токен авторизации
     * @return список данных рейтинга по предметам
     */
    public List<EjRankSubjectsResponseDTO> getRankSubjects(String personId, String authorization) {
        log.debug("Getting rank subjects for personId: {}", personId);
        
        String url = ejProperty.getUrl() + ejProperty.getRankSubjectsEndpoint();
        
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url)
                .queryParam("personId", personId);
        
        ResponseEntity<List<EjRankSubjectsResponseDTO>> response = ejRestTemplate.exchange(
                builder.build().toUri(),
                HttpMethod.GET,
                RestUtils.createSimpleHeaserEntity(null),
                new ParameterizedTypeReference<List<EjRankSubjectsResponseDTO>>() {}
        );
        
        List<EjRankSubjectsResponseDTO> result = Objects.requireNonNull(response.getBody());
        log.debug("Retrieved {} rank subjects records for personId: {}", result.size(), personId);
        
        return result;
    }
}
