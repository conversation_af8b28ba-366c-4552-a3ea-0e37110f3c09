package ru.portfolio.ax.service.settings;

import ru.portfolio.ax.rest.dto.UserSettingDTO;

public interface UserSettingService {
    /**
     * Сохраняем настройки пользователя
     */
    void saveSettings(String auth, String personId, UserSettingDTO settingDTO);

    /**
     * Получаем настройки пользователя
     */
    UserSettingDTO getSettings(String auth, String personId);

    /**
     * Получаем настройки пользователя
     */
    UserSettingDTO getSettingsByAdmin(String personId);
}
