package ru.portfolio.ax.service.settings.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import ru.portfolio.ax.model.ThemeSettings;
import ru.portfolio.ax.model.User;
import ru.portfolio.ax.model.UserSetting;
import ru.portfolio.ax.model.UserVisibilitySetting;
import ru.portfolio.ax.repository.ThemeSettingRepository;
import ru.portfolio.ax.repository.UserRepository;
import ru.portfolio.ax.repository.UserSettingRepository;
import ru.portfolio.ax.repository.UserVisibilitySettingRepository;
import ru.portfolio.ax.rest.dto.UserSettingDTO;
import ru.portfolio.ax.rest.dto.aupd.AccessTokenPayloadDto;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.service.settings.UserSettingService;
import ru.portfolio.ax.util.security.AuthService;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserSettingServiceImpl implements UserSettingService {
    final UserSettingRepository userSettingRepository;
    final AuthService authService;
    final ObjectMapper mapper;
    final UserRepository userRepository;
    final ThemeSettingRepository themeSettingRepository;
    final UserVisibilitySettingRepository visibilitySettingRepository;

    @Value("${roles.global.childId}")
    private Long childId;

    private final String defaultUserSettings = "default-user-setting.json";
    private String defaultUserSettingsChecksum;

    @PostConstruct
    private void init() throws IOException {
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream(defaultUserSettings);

        defaultUserSettingsChecksum = org.apache.commons.codec.digest.DigestUtils.sha512Hex(inputStream);

        log.info("User settings checksum: {}", defaultUserSettingsChecksum);
    }

    @Override
    @Transactional
    public void saveSettings(String auth, String personId, UserSettingDTO settingDTO) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(auth);
        //1
        User user = userRepository.findFirstByAupdId(tokenPayload.getSub()).orElse(null);

        if (Objects.isNull(user)) return;

        if (Objects.nonNull(settingDTO.getSetting())) {
            // При сохранении настроек проверяем их на соответствие дефолтным
            settingDTO.getSetting().setVisibilitySettings(
                    checkAndGetUpdatedSettings(getDefaultSetting().getVisibilitySettings(),
                            settingDTO.getSetting().getVisibilitySettings()));
            //2
            UserSetting setting = userSettingRepository.getUserSettingByUserIdAndPersonId(user.getAupdId(), personId).orElse(null);
            //3
            setting = fillUserSetting(personId, settingDTO, user, setting);
            setting.setSystemUpdateDate(LocalDateTime.now());
            userSettingRepository.saveAndFlush(setting);
        }
        List<User> parents = userRepository.findAllByChildrenContains(personId);
        for (User parent : parents) {
            UserSetting parentUserSetting = userSettingRepository.findFirstByUserIdAndPersonIdOrderByIdDesc(parent.getAupdId(), personId);
            if (nonNull(parentUserSetting) && nonNull(settingDTO.getThemeSetting())) {
                parentUserSetting.setThemeSetting(settingDTO.getThemeSetting());
                userSettingRepository.save(parentUserSetting);
            }
        }
        //4
       /* if (Objects.nonNull(settingDTO.getHide())) {
            processHiddenCards(UUID.fromString(personId), user, SettingSectionType.ACHIEVEMENT.getName(), settingDTO.getHide().getAchievements());
            processHiddenCards(UUID.fromString(personId), user, SettingSectionType.OLYMPIAD.getName(), settingDTO.getHide().getOlympiads());
        }*/
    }

    @Override
    @SneakyThrows
    @Transactional
    public UserSettingDTO getSettings(String auth, String personId) {
        //1
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(auth);
        //2
        UserSetting settings = userSettingRepository.findFirstByUserIdAndPersonIdOrderByIdDesc(tokenPayload.getSub(), personId);
        UserSettingDTO userSettingDTO = null;
        if (Objects.isNull(settings)) {
            userSettingDTO = new UserSettingDTO();
            userSettingDTO.setPersonId(personId);
            userSettingDTO.setSetting(getDefaultSetting());
            userSettingDTO.setSystemUpdateDate(LocalDateTime.now());
            saveSettings(auth, personId, userSettingDTO);
            settings = userSettingRepository.findFirstByUserIdAndPersonIdOrderByIdDesc(tokenPayload.getSub(), personId);
        } else {
            /*  Если контрольная сумма файла дефолтных настроек
                не равна сохраненной контрольной сумме пользовательских настроек - обновляем */
            if (!defaultUserSettingsChecksum.equals(settings.getDefaultSettingsChecksum())) {
                UserSettingDTO.SettingDTO defaultSetting = getDefaultSetting();
                userSettingDTO = UserSettingDTO.fromEntity(settings);
                userSettingDTO.getSetting().setVisibilitySettings(
                        checkAndGetUpdatedSettings(defaultSetting.getVisibilitySettings(),
                                userSettingDTO.getSetting().getVisibilitySettings()));
                userSettingDTO.setSystemUpdateDate(LocalDateTime.now());
                saveSettings(auth, personId, userSettingDTO);

                settings.setDefaultSettingsChecksum(defaultUserSettingsChecksum);
                userSettingRepository.save(settings);
            } else {
                userSettingDTO = UserSettingDTO.fromEntity(settings);
            }
        }
        ThemeSettings theme = themeSettingRepository.findAllById(settings.getThemeSetting())
                .stream().findFirst().orElse(null);
        userSettingDTO.setThemeSetting(Objects.nonNull(theme) ? theme.getId() : null);
        userSettingDTO.setThemeSettings(Objects.nonNull(theme) ? theme.getValue() : null);
        return userSettingDTO;
    }

    @Override
    @Transactional
    public UserSettingDTO getSettingsByAdmin(String personId) {
        //1
        List<User> parents = userRepository.findAllByChildrenContains(personId);
        List<Long> parentIds = parents.stream().map(User::getAupdId).collect(Collectors.toList());
        UserSetting parentSetting = userSettingRepository.findAllByPersonIdAndUserIdInOrderByCreationDateDesc(
                personId, parentIds).stream().findFirst().orElse(null);
        UserSettingDTO userSettingDTO;
        if (Objects.isNull(parentSetting)) {
            userSettingDTO = new UserSettingDTO();
            userSettingDTO.setPersonId(personId);
            userSettingDTO.setSetting(getDefaultSetting());
        } else {
            userSettingDTO = UserSettingDTO.fromEntity(parentSetting);
        }
        List<ThemeSettings> themes = themeSettingRepository.findByPersonIdAndRoleId(personId, childId.toString());
        ThemeSettings theme = themes.isEmpty() ? null : themes.get(0);
        userSettingDTO.setThemeSetting(Objects.nonNull(theme) ? theme.getId() : null);
        userSettingDTO.setThemeSettings(Objects.nonNull(theme) ? theme.getValue() : null);

        return userSettingDTO;
    }

    @SneakyThrows
    private UserSettingDTO.SettingDTO getDefaultSetting() {
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream("default-user-setting.json");
        return mapper.readValue(inputStream, UserSettingDTO.SettingDTO.class);
    }

    private UserSetting fillUserSetting(String personId, UserSettingDTO settingDTO, User user, UserSetting userSetting) {
        userSetting = UserSettingDTO.toEntity(settingDTO, userSetting);

        if (Objects.isNull(userSetting.getUserId())) userSetting.setUserId(user.getAupdId());
        if (Objects.isNull(userSetting.getCreationDate())) userSetting.setCreationDate(LocalDateTime.now());
        if (Objects.isNull(userSetting.getPersonId())) userSetting.setPersonId(personId);
        if (Objects.isNull(userSetting.getThemeSetting())) userSetting.setThemeSetting(1L); // default
        if (Objects.isNull(userSetting.getSystemUpdateDate()))
            userSetting.setSystemUpdateDate(LocalDateTime.now()); // default
        if (Objects.isNull(userSetting.getDefaultSettingsChecksum()))
            userSetting.setDefaultSettingsChecksum(defaultUserSettingsChecksum); // default

        return userSetting;
    }

    /**
     * Метод предназначен для обновления найденых пользовательских настроек основываясь на дефолтных
     *
     * @param defaultSettings
     * @param userSettings
     * @return
     */
    @SneakyThrows
    private List<UserSettingDTO.SettingSectionDTO> checkAndGetUpdatedSettings(List<UserSettingDTO.SettingSectionDTO> defaultSettings,
                                                                              List<UserSettingDTO.SettingSectionDTO> userSettings) {
        if (Objects.isNull(userSettings)) {
            userSettings = new ArrayList<>();
        }
        // Для каждой секции в дефолтных настройках ищем совпадающие в найденых пользовательских
        for (UserSettingDTO.SettingSectionDTO x : defaultSettings) {
            List<UserSettingDTO.SettingSectionDTO> userSections = userSettings.stream()
                    .filter(y -> y.getSectionTypeCode().equals(x.getSectionTypeCode())).collect(Collectors.toList());
            // Если найдено больше одной записи по коду - удаляем их все и добавляем их дефолтных
            if (userSections.size() > 1) {
                userSettings.removeAll(userSections);
                userSettings.add(x);
            }
            // Если найдена одна совпадающая по коду секция - проводим проверку по всем подкатегориям
            else if (userSections.size() == 1) {
                UserSettingDTO.SettingSectionDTO userSection = userSections.iterator().next();
                userSection.setName(x.getName());
                if (nonNull(x.getSubSections()) && !x.getSubSections().isEmpty()) {
                    userSection.setSubSections(checkAndGetUpdatedSettings(x.getSubSections(), userSection.getSubSections()));
                }
            }
            // Если не найдена совпадающая по коду секция добавляем дефолтную секцию со всеми подсекциями
            else {
                userSettings.add(x);
            }
        }

        List<String> validSectionCodes = defaultSettings.stream()
                .map(UserSettingDTO.SettingSectionDTO::getSectionTypeCode)
                .collect(Collectors.toList());
        userSettings.removeAll(userSettings.stream().filter(x -> !validSectionCodes.contains(x.getSectionTypeCode())).collect(Collectors.toList()));
        return userSettings;
    }

    @Transactional
    protected void processHiddenCards(UUID personId, User user, String sectionName,
                                      List<UserSettingDTO.Record> records) {
        if (CollectionUtils.isEmpty(records)) return;

        Collection<Long> settingsForDelete = records.stream().map(UserSettingDTO.Record::getSettingId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!settingsForDelete.isEmpty()) {
            PortfolioException.check(visibilitySettingRepository.existsAllByIdIn(settingsForDelete), PortfolioException.get999());

            visibilitySettingRepository.deleteAllBySectionAndIdIn(sectionName,
                    settingsForDelete);
        }

        List<String> hiddenCards = records.stream().filter(a -> Objects.isNull(a.getSettingId()))
                .map(UserSettingDTO.Record::getRecordId)
                .collect(Collectors.toList());

        List<Long> ids = new ArrayList<>();
        List<UUID> uuids = new ArrayList<>();

        hiddenCards.forEach(a -> {
            try {
                ids.add(Long.parseLong(a));
            } catch (NumberFormatException e) {
                uuids.add(UUID.fromString(a));
            }
        });
        Boolean check1 = !visibilitySettingRepository.existsAllByPersonIdAndUserIdAndSectionAndRecordIdIn(
                personId, user.getId(), sectionName, ids);
        Boolean check2 = !visibilitySettingRepository.existsAllByPersonIdAndUserIdAndSectionAndRecordUuidIn(
                personId, user.getId(), sectionName, uuids);

        PortfolioException.check(check1 && check2, PortfolioException.get999());

        List<UserVisibilitySetting> newHiddenCards = new ArrayList<>();
        ids.forEach(i -> newHiddenCards.add(new UserVisibilitySetting(personId, user, sectionName, i)));
        uuids.forEach(u -> newHiddenCards.add(new UserVisibilitySetting(personId, user, sectionName, u)));

        visibilitySettingRepository.saveAll(newHiddenCards);

    }
}
