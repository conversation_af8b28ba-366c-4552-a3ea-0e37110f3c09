package ru.portfolio.ax.service.giaExam.impl;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import ru.portfolio.ax.model.ShareLink;
import ru.portfolio.ax.model.enums.GiaExamForm;
import ru.portfolio.ax.repository.GiaExamRepository;
import ru.portfolio.ax.rest.dto.StandardExamDTO;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.service.giaExam.GiaExamService;
import ru.portfolio.ax.util.personUtils.PersonUtils;
import ru.portfolio.ax.util.shareLinkUtils.ShareLinkUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class GiaExamServiceImpl implements GiaExamService {
    ShareLinkUtils shareLinkUtils;
    GiaExamRepository giaExamRepository;
    PersonUtils personUtils;

    @Override
    public List<StandardExamDTO> getExamsByGuid(String guid, String share) {
        return giaExamRepository.findAllByPersonIdsAndExamFormCodes(
                        personUtils.getPersonIds(guid),
                        getAvailableGiaFormCodes(share)
                ).stream()
                .map(StandardExamDTO::build)
                .collect(Collectors.toList());
    }

    private List<Integer> getAvailableGiaFormCodes(String share) {
        if (Objects.isNull(share)) {
            return Collections.emptyList();
        }

        ShareLink shareLink = shareLinkUtils.parseCookie(share);

        PortfolioException.check(
                Objects.nonNull(shareLink) && shareLink.getGia(),
                PortfolioException.get461()
        );

        List<Integer> result = new ArrayList<>();

        if (Objects.nonNull(shareLink.getEge()) && shareLink.getEge()) {
            result.add(GiaExamForm.EGE.code());
        }
        if (Objects.nonNull(shareLink.getOge()) && shareLink.getOge()) {
            result.add(GiaExamForm.OGE.code());
        }
        if (Objects.nonNull(shareLink.getGve9()) && shareLink.getGve9()) {
            result.add(GiaExamForm.GVE9.code());
        }
        if (Objects.nonNull(shareLink.getGve11()) && shareLink.getGve11()) {
            result.add(GiaExamForm.GVE11.code());
        }
        if (nonNull(shareLink.getOther()) && shareLink.getOther()) {
            result.addAll(GiaExamForm.getOther());
        }

        return result;
    }

    @Override
    public List<StandardExamDTO> getExamsByGuidPageable(String guid) {
        return giaExamRepository.findAllByPersonIdsAndExamFormCodes(
                        personUtils.getPersonIds(guid),
                        Collections.emptyList()
                ).stream()
                .map(StandardExamDTO::build)
                .collect(Collectors.toList());
    }
}
