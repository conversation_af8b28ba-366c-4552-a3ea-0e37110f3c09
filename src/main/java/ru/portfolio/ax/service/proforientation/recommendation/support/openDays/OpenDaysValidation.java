package ru.portfolio.ax.service.proforientation.recommendation.support.openDays;

import lombok.NonNull;
import ru.portfolio.ax.service.proforientation.recommendation.enums.LearnerCategoryEnum;

import java.util.HashSet;
import java.util.Set;

public class OpenDaysValidation {
    public static boolean validate(@NonNull LearnerCategoryEnum learnerCategory) {
        return validateLearnerCategory(learnerCategory);

    }

    /**
     * Только для учеников 8-11 класс и их родителей
     */
    private static boolean validateLearnerCategory(LearnerCategoryEnum learnerCategory) {
        Set<LearnerCategoryEnum> approvedLearnerCategoryEnum = new HashSet<LearnerCategoryEnum>() {{
            add(LearnerCategoryEnum.CLASS_8);
            add(LearnerCategoryEnum.CLASS_9);
            add(LearnerCategoryEnum.CLASS_10);
            add(LearnerCategoryEnum.CLASS_11);
        }};

        return approvedLearnerCategoryEnum.contains(learnerCategory);
    }
}
