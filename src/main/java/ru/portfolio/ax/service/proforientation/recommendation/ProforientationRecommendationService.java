package ru.portfolio.ax.service.proforientation.recommendation;

import ru.portfolio.ax.rest.dto.GetRecommendationProforientationResponse;

public interface ProforientationRecommendationService {
    /**
     * Получение рекомендаций на основе пройденной профориентации
     */
    GetRecommendationProforientationResponse getData(String personId,
                                                     String classLevel);
}
