package ru.portfolio.ax.service.proforientation.recommendation.impl;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import ru.portfolio.ax.model.Applications;
import ru.portfolio.ax.model.ref.LearnerCategoryRef;
import ru.portfolio.ax.repository.ApplicationsRepository;
import ru.portfolio.ax.repository.ref.LearnerCategoryRefRepository;
import ru.portfolio.ax.rest.dto.GetRecommendationProforientationResponse;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.service.ext.ProforientationService;
import ru.portfolio.ax.service.proforientation.recommendation.ProforientationRecommendationExcursionsService;
import ru.portfolio.ax.service.proforientation.recommendation.ProforientationRecommendationOpenDaysService;
import ru.portfolio.ax.service.proforientation.recommendation.ProforientationRecommendationProfessionalTestsService;
import ru.portfolio.ax.service.proforientation.recommendation.ProforientationRecommendationService;
import ru.portfolio.ax.service.proforientation.recommendation.enums.LearnerCategoryEnum;
import ru.portfolio.ax.service.proforientation.recommendation.support.ProforientationRecommendationValidation;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ProforientationRecommendationServiceImpl implements ProforientationRecommendationService {
    ApplicationsRepository applicationsRepository;
    LearnerCategoryRefRepository learnerCategoryRefRepository;
    ProforientationService proforientationService;

    ProforientationRecommendationProfessionalTestsService proforientationRecommendationProfessionalTestsService;
    ProforientationRecommendationExcursionsService proforientationRecommendationExcursionsService;
    ProforientationRecommendationOpenDaysService proforientationRecommendationOpenDaysService;

    @Override
    public GetRecommendationProforientationResponse getData(String personId,
                                                            String classLevel) {
        GetRecommendationProforientationResponse result = new GetRecommendationProforientationResponse();

        if (!ProforientationRecommendationValidation.validate(personId, classLevel)) {
            return new GetRecommendationProforientationResponse();
        }

        LearnerCategoryEnum learnerCategory = getLearnerCategoryEnum(classLevel);

        /* Список мероприятий, на которые ученик уже зарегистрировался */
        List<String> alreadyRegisteredEventIds = getAlreadyRegisteredEventIds(personId);

        /* Данные с рекомендациями */
        JsonNode recommendations = proforientationService.getRecommendations(personId).getBody();

        /* Профессиональные пробы */
        result.setRecommendationsProfTests(
                proforientationRecommendationProfessionalTestsService.getData(
                        learnerCategory,
                        alreadyRegisteredEventIds,
                        recommendations
                )
        );

        /* Экскурсии к работодателям */
        result.setRecommendationsExcursions(
                proforientationRecommendationExcursionsService.getData(
                        learnerCategory,
                        alreadyRegisteredEventIds,
                        recommendations
                )
        );

        /* Дни открытых дверей */
        result.setRecommendationsDod(
                proforientationRecommendationOpenDaysService.getData(
                        learnerCategory,
                        alreadyRegisteredEventIds,
                        recommendations
                )
        );

        return result;
    }

    private LearnerCategoryEnum getLearnerCategoryEnum(String classLevel) {
        LearnerCategoryRef learnerCategoryRef = learnerCategoryRefRepository.findByValue(classLevel + '%');

        if (learnerCategoryRef == null) {
            throw PortfolioException.get424();
        }

        int learnerCategoryRefCode = learnerCategoryRef.getCode();

        return Arrays.stream(LearnerCategoryEnum.values())
                .filter(learnerCategoryEnum -> learnerCategoryEnum.getCode() == learnerCategoryRefCode)
                .findFirst()
                .orElseThrow(PortfolioException::get424);
    }

    /*
     * Получаем список заявок ученика на мероприятия
     */
    private List<String> getAlreadyRegisteredEventIds(String personId) {
        return applicationsRepository.findByPersonId(personId).stream()
                .map(Applications::getEventId)
                .collect(Collectors.toList());
    }
}
