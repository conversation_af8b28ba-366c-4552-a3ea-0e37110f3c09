package ru.portfolio.ax.service.proforientation.recommendation.support.openDays;

import lombok.NonNull;
import ru.portfolio.ax.model.AfishaEvents;
import ru.portfolio.ax.model.SpecialitiesByIndustry;
import ru.portfolio.ax.rest.dto.GetRecommendationProforientationResponse;
import ru.portfolio.ax.service.proforientation.recommendation.support.ProforientationRecommendationUtils;
import ru.portfolio.ax.util.ResponseDefaultValue;

import java.util.*;
import java.util.stream.Collectors;

public class OpenDaysMapper {
    public static List<GetRecommendationProforientationResponse.RecommendDod> execute(@NonNull Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEvents) {
        return afishaEvents.entrySet().stream()
                .sorted(ProforientationRecommendationUtils.getAfishaEventsComparator())
                .map(entry -> {
                    AfishaEvents afishaEvent = entry.getKey();
                    Set<SpecialitiesByIndustry> specialitiesByIndustry = entry.getValue();

                    return execute(
                            afishaEvent,
                            specialitiesByIndustry
                    );
                })
                .collect(Collectors.toList());
    }

    private static GetRecommendationProforientationResponse.RecommendDod execute(AfishaEvents afishaEvent,
                                                                                 Set<SpecialitiesByIndustry> specialitiesByIndustry) {
        GetRecommendationProforientationResponse.RecommendDod recommendOpenDays = new GetRecommendationProforientationResponse.RecommendDod();

        recommendOpenDays.setEventId(ResponseDefaultValue.getDefaultValue(afishaEvent.getEventId()));
        recommendOpenDays.setDodDate(afishaEvent.getStartAt().toLocalDate());
        recommendOpenDays.setAddressDod(ResponseDefaultValue.getDefaultValue(afishaEvent.getAddressPlace()));
        recommendOpenDays.setName(ResponseDefaultValue.getDefaultValue(afishaEvent.getCollegeName()));
        recommendOpenDays.setDescription(ResponseDefaultValue.getDefaultValue(afishaEvent.getDescription()));
        recommendOpenDays.setUrl(ResponseDefaultValue.getDefaultValue(afishaEvent.getEventUrl()));
        recommendOpenDays.setEndTime(afishaEvent.getEndAt());
        recommendOpenDays.setStartTime(afishaEvent.getStartAt());

        recommendOpenDays.setIndustryAndSpecs(
                getIndustryAndSpecs(specialitiesByIndustry)
        );

        return recommendOpenDays;
    }

    private static List<GetRecommendationProforientationResponse.Industry> getIndustryAndSpecs(Set<SpecialitiesByIndustry> specialitiesByIndustry) {
        /*Map<CodeIndustry, GetRecommendationProforientationResponse.Industry>*/
        Map<String, GetRecommendationProforientationResponse.Industry> industryMap = new HashMap<>();

        specialitiesByIndustry.forEach(specialityByIndustry -> {
            String codeIndustry = specialityByIndustry.getCodeIndustry();

            GetRecommendationProforientationResponse.Industry industry = industryMap.getOrDefault(
                    codeIndustry,
                    createNewIndustry(specialityByIndustry)
            );

            industry.getSpecialities().add(getSpeciality(specialityByIndustry));

            industryMap.put(codeIndustry, industry);
        });

        return new ArrayList<>(industryMap.values());
    }

    private static GetRecommendationProforientationResponse.Industry createNewIndustry(SpecialitiesByIndustry specialityByIndustry) {
        GetRecommendationProforientationResponse.Industry industry = new GetRecommendationProforientationResponse.Industry();

        industry.setIndustryCode(ResponseDefaultValue.getDefaultValue(specialityByIndustry.getCodeIndustry()));
        industry.setIndustryName(ResponseDefaultValue.getDefaultValue(specialityByIndustry.getNameIndustry()));

        industry.setSpecialities(new ArrayList<>());

        return industry;
    }

    private static GetRecommendationProforientationResponse.Spec getSpeciality(SpecialitiesByIndustry specialityByIndustry) {
        GetRecommendationProforientationResponse.Spec speciality = new GetRecommendationProforientationResponse.Spec();

        speciality.setSpecCode(ResponseDefaultValue.getDefaultValue(specialityByIndustry.getCodeSpeciality()));
        speciality.setSpecName(ResponseDefaultValue.getDefaultValue(specialityByIndustry.getNameSpeciality()));

        return speciality;
    }
}
