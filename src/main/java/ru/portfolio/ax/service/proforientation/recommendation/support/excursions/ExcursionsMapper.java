package ru.portfolio.ax.service.proforientation.recommendation.support.excursions;

import lombok.NonNull;
import ru.portfolio.ax.model.AfishaEvents;
import ru.portfolio.ax.model.SpecialitiesByIndustry;
import ru.portfolio.ax.rest.dto.GetRecommendationProforientationResponse;
import ru.portfolio.ax.service.proforientation.recommendation.dtos.AfishaEventIndustryDto;
import ru.portfolio.ax.service.proforientation.recommendation.support.ProforientationRecommendationUtils;
import ru.portfolio.ax.util.ResponseDefaultValue;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class ExcursionsMapper {
    public static List<GetRecommendationProforientationResponse.RecommendExcursion> execute(@NonNull Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEvents) {
        return afishaEvents.entrySet().stream()
                .sorted(ProforientationRecommendationUtils.getAfishaEventsComparator())
                .map(entry -> {
                    AfishaEvents afishaEvent = entry.getKey();
                    Set<SpecialitiesByIndustry> specialitiesByIndustry = entry.getValue();

                    return execute(
                            afishaEvent,
                            specialitiesByIndustry
                    );
                })
                .collect(Collectors.toList());
    }

    private static GetRecommendationProforientationResponse.RecommendExcursion execute(AfishaEvents afishaEvent,
                                                                                       Set<SpecialitiesByIndustry> specialitiesByIndustry) {
        GetRecommendationProforientationResponse.RecommendExcursion excursion = new GetRecommendationProforientationResponse.RecommendExcursion();

        excursion.setEventId(ResponseDefaultValue.getDefaultValue(afishaEvent.getEventId()));
        excursion.setDescription(ResponseDefaultValue.getDefaultValue(afishaEvent.getDescription()));
        excursion.setDistrict(ResponseDefaultValue.getDefaultValue(afishaEvent.getDistrict()));
        excursion.setName(ResponseDefaultValue.getDefaultValue(afishaEvent.getEventName()));
        excursion.setExcursionDate(afishaEvent.getStartAt().toLocalDate());
        excursion.setEndTime(afishaEvent.getEndAt());
        excursion.setStartTime(afishaEvent.getStartAt());

        AfishaEventIndustryDto industry = ProforientationRecommendationUtils.getAfishaEventIndustryDto(specialitiesByIndustry);
        excursion.setIndustryCode(ResponseDefaultValue.getDefaultValue(ProforientationRecommendationUtils.getIndustryCode(industry)));
        excursion.setIndustryName(ResponseDefaultValue.getDefaultValue(ProforientationRecommendationUtils.getIndustryName(industry)));

        excursion.setPlaceExcursion(ResponseDefaultValue.getDefaultValue(afishaEvent.getEmployerName()));

        return excursion;
    }
}
