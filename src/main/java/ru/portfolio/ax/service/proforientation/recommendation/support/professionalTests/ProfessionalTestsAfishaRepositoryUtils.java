package ru.portfolio.ax.service.proforientation.recommendation.support.professionalTests;

import lombok.NonNull;
import ru.portfolio.ax.model.AfishaEvents;
import ru.portfolio.ax.model.SpecialitiesByIndustry;

import java.time.LocalDateTime;
import java.util.*;

public class ProfessionalTestsAfishaRepositoryUtils {

    /**
     * Конвертируем DTO в события для рекомендованых мероприятий
     */
    public static Map<AfishaEvents, Set<SpecialitiesByIndustry>> getProforientationRecommendations(@NonNull List<?> resultList) {
        Map<AfishaEvents, Set<SpecialitiesByIndustry>> result = new HashMap<>();

        resultList.forEach(specialitiesByIndustryToAfishaEvents -> {
            SpecialitiesByIndustry specialitiesByIndustry = (SpecialitiesByIndustry) ((Object[]) specialitiesByIndustryToAfishaEvents)[0];
            AfishaEvents afishaEvents = (AfishaEvents) ((Object[]) specialitiesByIndustryToAfishaEvents)[1];

            Set<SpecialitiesByIndustry> afishaEventsSpecialitiesByIndustry = result.getOrDefault(afishaEvents, new HashSet<>());
            afishaEventsSpecialitiesByIndustry.add(specialitiesByIndustry);

            result.put(afishaEvents, afishaEventsSpecialitiesByIndustry);
        });

        return result;
    }

    /**
     * Период между текущей датой и дата проведения мероприятия не менее суток
     */
    public static LocalDateTime getCurrentDateWithDiff() {
        return LocalDateTime.now()
                .plusDays(1);
    }

    /**
     * Для правильной фильтрации нужно передавать не пустой список, а со значением, которое гарантировано не будет найдено
     */
    public static Collection<String> getDefaultEmptyList(Collection<String> list) {
        if (list == null
                || list.isEmpty()) {
            return Collections.singletonList("C");
        }

        return list;
    }
}
