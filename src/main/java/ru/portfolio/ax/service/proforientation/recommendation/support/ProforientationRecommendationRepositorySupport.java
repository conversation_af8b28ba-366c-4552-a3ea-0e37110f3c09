package ru.portfolio.ax.service.proforientation.recommendation.support;

import java.util.Collections;
import java.util.List;

import static org.springframework.util.CollectionUtils.isEmpty;

public class ProforientationRecommendationRepositorySupport {
    /**
     * Полчему-то запрос в БД падает, если передавать в него пустой список
     * Поэтому если переданный список пустой, то возвращаем "заглушку"
     */
    public static final List<String> emptyListFix = Collections.singletonList("C");

    public static List<String> addEmptyStringIfListEmpty(List<String> list) {
        return !isEmpty(list)
                ? list
                : emptyListFix;
    }
}
