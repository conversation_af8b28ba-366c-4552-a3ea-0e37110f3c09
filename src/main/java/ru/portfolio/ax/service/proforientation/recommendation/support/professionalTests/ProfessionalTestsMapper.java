package ru.portfolio.ax.service.proforientation.recommendation.support.professionalTests;

import lombok.NonNull;
import ru.portfolio.ax.model.AfishaEvents;
import ru.portfolio.ax.model.SpecialitiesByIndustry;
import ru.portfolio.ax.rest.dto.GetRecommendationProforientationResponse;
import ru.portfolio.ax.service.proforientation.recommendation.dtos.AfishaEventIndustryDto;
import ru.portfolio.ax.service.proforientation.recommendation.support.ProforientationRecommendationUtils;
import ru.portfolio.ax.util.ResponseDefaultValue;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class ProfessionalTestsMapper {
    public static List<GetRecommendationProforientationResponse.RecommendationProfTest> execute(@NonNull Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEvents) {
        return afishaEvents.entrySet().stream()
                .sorted(ProforientationRecommendationUtils.getAfishaEventsComparator())
                .map(entry -> {
                    AfishaEvents afishaEvent = entry.getKey();
                    Set<SpecialitiesByIndustry> specialitiesByIndustry = entry.getValue();

                    return execute(
                            afishaEvent,
                            specialitiesByIndustry
                    );
                })
                .collect(Collectors.toList());
    }

    private static GetRecommendationProforientationResponse.RecommendationProfTest execute(AfishaEvents afishaEvent,
                                                                                           Set<SpecialitiesByIndustry> specialitiesByIndustry) {
        GetRecommendationProforientationResponse.RecommendationProfTest proforientationTest = new GetRecommendationProforientationResponse.RecommendationProfTest();

        proforientationTest.setEventId(ResponseDefaultValue.getDefaultValue(afishaEvent.getEventId()));
        proforientationTest.setName(ResponseDefaultValue.getDefaultValue(afishaEvent.getEventName()));
        proforientationTest.setProfTestDate(afishaEvent.getStartAt().toLocalDate());
        proforientationTest.setDistrict(ResponseDefaultValue.getDefaultValue(afishaEvent.getDistrict()));
        proforientationTest.setDescription(ResponseDefaultValue.getDefaultValue(afishaEvent.getDescription()));
        proforientationTest.setStartTime(afishaEvent.getStartAt());
        proforientationTest.setEndTime(afishaEvent.getEndAt());
        proforientationTest.setPlaceProfTest(ResponseDefaultValue.getDefaultValue(afishaEvent.getCollegeName()));

        AfishaEventIndustryDto industry = ProforientationRecommendationUtils.getAfishaEventIndustryDto(specialitiesByIndustry);
        proforientationTest.setIndustryCode(ResponseDefaultValue.getDefaultValue(ProforientationRecommendationUtils.getIndustryCode(industry)));
        proforientationTest.setIndustryName(ResponseDefaultValue.getDefaultValue(ProforientationRecommendationUtils.getIndustryName(industry)));

        proforientationTest.setSpecialities(
                getSpecialities(specialitiesByIndustry)
        );

        return proforientationTest;
    }

    private static List<GetRecommendationProforientationResponse.Spec> getSpecialities(Set<SpecialitiesByIndustry> specialitiesByIndustry) {
        return specialitiesByIndustry.stream()
                .map(ProfessionalTestsMapper::getSpecialities)
                .collect(Collectors.toList());
    }

    private static GetRecommendationProforientationResponse.Spec getSpecialities(SpecialitiesByIndustry speciality) {
        GetRecommendationProforientationResponse.Spec spec = new GetRecommendationProforientationResponse.Spec();

        spec.setSpecName(ResponseDefaultValue.getDefaultValue(speciality.getNameSpeciality()));
        spec.setSpecCode(ResponseDefaultValue.getDefaultValue(speciality.getCodeSpeciality()));

        return spec;
    }
}
