package ru.portfolio.ax.service.proforientation.recommendation.support.excursions;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.NonNull;
import ru.portfolio.ax.service.proforientation.recommendation.enums.LearnerCategoryEnum;
import ru.portfolio.ax.service.proforientation.recommendation.support.ProforientationRecommendationRecommendationUtils;

import java.util.Set;

public class ExcursionsValidation {
    public static boolean validate(@NonNull LearnerCategoryEnum learnerCategory,
                                   JsonNode recommendations) {
        return validateLearnerCategory(learnerCategory)
                && validateRecommendationIndustry(recommendations);

    }

    /**
     * Только для 9-ти классников и их родителей
     */
    private static boolean validateLearnerCategory(LearnerCategoryEnum learnerCategory) {
        return LearnerCategoryEnum.CLASS_9.equals(learnerCategory);
    }

    /**
     * Только для тех, у кого есть рекомендации
     */
    private static boolean validateRecommendationIndustry(JsonNode recommendations) {
        if (recommendations == null) {
            return false;
        }

        Set<String> recommendationIndustryNames = ProforientationRecommendationRecommendationUtils.getIndustryCodes(recommendations);

        return !recommendationIndustryNames.isEmpty();
    }
}
