package ru.portfolio.ax.service.proforientation.recommendation.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * Параллель, в которой находится ученик
 */
@AllArgsConstructor
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public enum LearnerCategoryEnum {
    CLASS_8(14),
    CLASS_9(15),
    CLASS_10(16),
    CLASS_11(17);

    int code;
}
