package ru.portfolio.ax.service.proforientation.recommendation.support;

import lombok.extern.slf4j.Slf4j;
import ru.portfolio.ax.rest.exception.PortfolioException;

@Slf4j
public class ProforientationRecommendationValidation {
    /**
     * Валидация входных параметров
     */
    public static boolean validate(String personId,
                                   String classLevel) {
        isNotNullPersonId(personId);
        isNotNullClassLevel(classLevel);

        return isClassLevelNotContainChar(classLevel);
    }

    private static void isNotNullPersonId(String personId) {
        if (personId == null) {
            throw PortfolioException.get442("personId");
        }
    }

    private static void isNotNullClassLevel(String classLevel) {
        if (classLevel == null) {
            throw PortfolioException.get442("classLevel");
        }
    }

    private static boolean isClassLevelNotContainChar(String classLevel) {
        try {
            Integer.parseInt(classLevel.trim());
        } catch (Exception e) {
            log.error("Переданный classLevel нельзя конвертировать в int");
            return false;
        }

        return true;
    }
}
