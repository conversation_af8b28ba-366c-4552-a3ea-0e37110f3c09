package ru.portfolio.ax.service.proforientation.recommendation.impl;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Service;
import ru.portfolio.ax.model.AfishaEvents;
import ru.portfolio.ax.model.SpecialitiesByIndustry;
import ru.portfolio.ax.repository.AfishaEventsRepository;
import ru.portfolio.ax.rest.dto.GetRecommendationProforientationResponse;
import ru.portfolio.ax.service.proforientation.recommendation.ProforientationRecommendationExcursionsService;
import ru.portfolio.ax.service.proforientation.recommendation.enums.LearnerCategoryEnum;
import ru.portfolio.ax.service.proforientation.recommendation.support.ProforientationRecommendationRecommendationUtils;
import ru.portfolio.ax.service.proforientation.recommendation.support.ProforientationRecommendationUtils;
import ru.portfolio.ax.service.proforientation.recommendation.support.excursions.ExcursionsMapper;
import ru.portfolio.ax.service.proforientation.recommendation.support.excursions.ExcursionsValidation;

import java.util.*;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ProforientationRecommendationExcursionsServiceImpl implements ProforientationRecommendationExcursionsService {
    AfishaEventsRepository afishaEventsRepository;

    int EVENT_TYPE = 42; // Код типа "Профессиональная проба"
    int EVENTS_LIMIT = 20;

    @Override
    public List<GetRecommendationProforientationResponse.RecommendExcursion> getData(@NonNull LearnerCategoryEnum learnerCategory,
                                                                                     @NonNull List<String> alreadyRegisteredEventIds,
                                                                                     JsonNode recommendations) {
        if (!ExcursionsValidation.validate(learnerCategory, recommendations)) {
            return Collections.emptyList();
        }

        Map<AfishaEvents, Set<SpecialitiesByIndustry>> resultAfishaEvents = new HashMap<>();

        Map<AfishaEvents, Set<SpecialitiesByIndustry>> afishaEventsByIndustryCodes = getAfishaEventsByIndustryCodes(
                alreadyRegisteredEventIds,
                recommendations
        );
        ProforientationRecommendationUtils.unionAfishaEventsMap(resultAfishaEvents, afishaEventsByIndustryCodes);

        return ExcursionsMapper.execute(
                ProforientationRecommendationUtils.limitAfishaEvents(
                        resultAfishaEvents,
                        EVENTS_LIMIT
                )
        );
    }

    private Map<AfishaEvents, Set<SpecialitiesByIndustry>> getAfishaEventsByIndustryCodes(List<String> alreadyRegisteredEventIds,
                                                                                          JsonNode recommendations) {
        Set<String> industryCodes = ProforientationRecommendationRecommendationUtils.getIndustryCodes(recommendations);

        return afishaEventsRepository.findProforientationRecommendations(
                EVENT_TYPE,
                alreadyRegisteredEventIds,
                industryCodes,
                null,
                null,
                null
        );
    }
}
