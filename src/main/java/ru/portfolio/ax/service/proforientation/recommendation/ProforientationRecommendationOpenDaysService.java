package ru.portfolio.ax.service.proforientation.recommendation;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.NonNull;
import ru.portfolio.ax.rest.dto.GetRecommendationProforientationResponse;
import ru.portfolio.ax.service.proforientation.recommendation.enums.LearnerCategoryEnum;

import java.util.List;

public interface ProforientationRecommendationOpenDaysService {
    /**
     * Получение рекомендаций экскурсий к работодателям
     */
    List<GetRecommendationProforientationResponse.RecommendDod> getData(@NonNull LearnerCategoryEnum learnerCategory,
                                                                        @NonNull List<String> alreadyRegisteredEventIds,
                                                                        JsonNode recommendations);
}
