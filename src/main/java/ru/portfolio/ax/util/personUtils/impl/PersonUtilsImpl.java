package ru.portfolio.ax.util.personUtils.impl;

import com.google.common.collect.Sets;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.model.PersonIdUpdate;
import ru.portfolio.ax.repository.PersonUpdateRepository;
import ru.portfolio.ax.repository.common.PersonId;
import ru.portfolio.ax.util.personUtils.PersonUtils;

import java.util.Collection;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class PersonUtilsImpl implements PersonUtils {
    PersonUpdateRepository personUpdateRepository;

    @Override
    @ReadOnly
    @Transactional
    public Collection<String> getPersonIds(@NonNull String personGuid) {
        Optional<PersonId> optionalPersonId = personUpdateRepository.findPersonIds(personGuid);
        if (!optionalPersonId.isPresent()) {
            return Sets.newHashSet(personGuid);
        }

        ru.portfolio.ax.repository.common.PersonId personIdDTO = optionalPersonId.get();
        Set<String> personIds = Sets.newHashSet(StringUtils.split(personIdDTO.getPerson_ids(), ", "));

        Optional<PersonIdUpdate> firstByMeshIdOrNsi1IdOrNsi2Id = personUpdateRepository
                .findFirstByMeshIdOrNsi1IdOrNsi2IdOrderByCreationDateDesc(personGuid, personGuid, personGuid);

        if (firstByMeshIdOrNsi1IdOrNsi2Id.isPresent()) {
            PersonIdUpdate personIdUpdate = firstByMeshIdOrNsi1IdOrNsi2Id.get();
            if (!personIdUpdate.getMeshId().equals(personGuid)) {
                switch (personIdUpdate.getAction()) {
                    case ADD:
                        personIds.add(personIdUpdate.getNsi1Id());
                        personIds.add(personIdUpdate.getNsi2Id());
                        break;
                    case DELETE:
                        personIds.remove(personIdUpdate.getNsi1Id());
                        personIds.remove(personIdUpdate.getNsi2Id());
                        break;
                }
                personIds.remove(null);
            }
        }

        personIds.add(personGuid);

        return personIds;
    }
}
