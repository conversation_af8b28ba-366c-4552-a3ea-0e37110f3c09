package ru.portfolio.ax.util.shareLinkUtils.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import ru.portfolio.ax.model.ShareLink;
import ru.portfolio.ax.repository.ShareLinkRepository;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.util.shareLinkUtils.ShareLinkUtils;

import java.util.Base64;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ShareLinkUtilsImpl implements ShareLinkUtils {
    ObjectMapper objectMapper;
    ShareLinkRepository shareLinkRepository;

    @Override
    @Nullable
    @SneakyThrows
    public ShareLink parseCookie(String share) {
        if (StringUtils.isEmpty(share)) return null;

        byte[] base64 = Base64.getDecoder().decode(share);
        ShareLink shareLinkQuery = objectMapper.readValue(base64, ShareLink.class);

        Optional<ShareLink> link = Optional.of(shareLinkRepository.findByUrlAndIsActive(shareLinkQuery.getUrl(), true));

        return link.orElseThrow(PortfolioException::get461);
    }
}
