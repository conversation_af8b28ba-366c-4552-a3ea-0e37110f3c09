package ru.portfolio.ax.util;

/**
 * Дефолтные значения для обязательных полей
 */
public class ResponseDefaultValue {
    public static String getDefaultValue(String value) {
        return value != null
                ? value
                : "";
    }

    public static Double getDefaultValue(Double value) {
        return value != null
                ? value
                : 0d;
    }

    public static Integer getDefaultValue(Integer value) {
        return value != null
                ? value
                : 0;
    }
}
