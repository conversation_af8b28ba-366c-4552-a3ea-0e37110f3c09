package ru.portfolio.ax.configuration;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.Arrays;

/**
 * Конфигурация кеширования
 */
@Configuration
@EnableCaching
public class CacheConfiguration {

    /**
     * Имя кеша для данных рейтинга класса из API ЭЖ
     */
    public static final String EJ_RANK_CLASS_CACHE = "ejRankClass";

    /**
     * Имя кеша для данных рейтинга по предметам из API ЭЖ
     */
    public static final String EJ_RANK_SUBJECTS_CACHE = "ejRankSubjects";

    /**
     * Конфигурация менеджера кеша с TTL на 1 час
     */
    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        // Настройка кеша: TTL 1 час, максимум 1000 записей
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(Duration.ofHours(1))
                .maximumSize(1000)
                .recordStats());

        // Указываем имена кешей
        cacheManager.setCacheNames(Arrays.asList(
                EJ_RANK_CLASS_CACHE,
                EJ_RANK_SUBJECTS_CACHE
        ));

        return cacheManager;
    }
}
