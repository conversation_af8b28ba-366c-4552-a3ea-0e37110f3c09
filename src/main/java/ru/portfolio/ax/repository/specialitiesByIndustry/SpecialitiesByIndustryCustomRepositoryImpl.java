package ru.portfolio.ax.repository.specialitiesByIndustry;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Repository;
import ru.portfolio.ax.model.SpecialitiesByIndustry;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import java.util.List;

@Repository
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpecialitiesByIndustryCustomRepositoryImpl implements SpecialitiesByIndustryCustomRepository {
    @PersistenceContext
    protected EntityManager em;

    @Override
    public List<SpecialitiesByIndustry> findByNotArchivePrioritySpecialitiesRef() {
        TypedQuery<SpecialitiesByIndustry> query =
                em.createQuery("SELECT " +
                                "specialitiesByIndustry " +
                                "FROM SpecialitiesByIndustry specialitiesByIndustry " +
                                "   INNER JOIN PrioritySpecialitiesRef prioritySpecialitiesRef " +
                                "   ON prioritySpecialitiesRef.nameIndustry = specialitiesByIndustry.nameIndustry " +
                                "       AND prioritySpecialitiesRef.isArchive = FALSE "
                        , SpecialitiesByIndustry.class);

        return query.getResultList();
    }
}
