package ru.portfolio.ax.repository.giaExam;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Repository;
import ru.portfolio.ax.model.GiaExam;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static org.springframework.util.CollectionUtils.isEmpty;

@Repository
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class GiaExamRepositoryCustomRepositoryImpl implements GiaExamRepositoryCustomRepository {
    @PersistenceContext
    protected EntityManager em;

    @Override
    public List<GiaExam> findAllByPersonIdsAndExamFormCodes(Collection<String> personIds,
                                                            Collection<Integer> examFormCodes) {
        if (isEmpty(personIds)) {
            return Collections.emptyList();
        }

        TypedQuery<GiaExam> query =
                em.createQuery("SELECT " +
                                "   giaExam " +
                                "FROM GiaExam giaExam " +
                                "   LEFT JOIN FETCH giaExam.examForm examForm " +
                                "WHERE " +
                                "   giaExam.personId IN :personIds " +
                                (!isEmpty(examFormCodes) ? "AND giaExam.examForm.code IN :examFormCodes " : "") +
                                "   AND (giaExam.isDeleted IS NULL OR giaExam.isDeleted = FALSE) "
                        , GiaExam.class);

        query.setParameter("personIds", personIds);

        if (!isEmpty(examFormCodes)) {
            query.setParameter("examFormCodes", examFormCodes);
        }

        return query.getResultList();
    }
}
