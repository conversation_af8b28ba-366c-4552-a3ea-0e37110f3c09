package ru.portfolio.ax.repository.afishaEvents;

import lombok.NonNull;
import ru.portfolio.ax.model.AfishaEvents;
import ru.portfolio.ax.model.SpecialitiesByIndustry;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

public interface AfishaEventsCustomRepository {
    /**
     * Ищем события для рекомендованых мероприятий
     * Если не нужно фильтровать по полям, то передаем в них null:
     * collegeNames
     * specialityCodes
     */
    Map<AfishaEvents, Set<SpecialitiesByIndustry>> findProforientationRecommendations(int type,
                                                                                      @NonNull Collection<String> excludedEventIds,
                                                                                      @NonNull Collection<String> industryCodes,
                                                                                      Collection<String> collegeNames,
                                                                                      Collection<String> collegeCodes,
                                                                                      Collection<String> specialityCodes);
}
