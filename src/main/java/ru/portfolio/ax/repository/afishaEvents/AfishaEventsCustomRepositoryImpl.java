package ru.portfolio.ax.repository.afishaEvents;

import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Repository;
import ru.portfolio.ax.model.AfishaEvents;
import ru.portfolio.ax.model.SpecialitiesByIndustry;
import ru.portfolio.ax.service.proforientation.recommendation.support.professionalTests.ProfessionalTestsAfishaRepositoryUtils;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.*;

@Repository
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AfishaEventsCustomRepositoryImpl implements AfishaEventsCustomRepository {
    @PersistenceContext
    protected EntityManager em;

    @Override
    public Map<AfishaEvents, Set<SpecialitiesByIndustry>> findProforientationRecommendations(int type,
                                                                                             @NonNull Collection<String> excludedEventIds,
                                                                                             @NonNull Collection<String> industryCodes,
                                                                                             Collection<String> collegeNames,
                                                                                             Collection<String> collegeCodes,
                                                                                             Collection<String> specialityCodes) {
        Query query =
                em.createQuery("SELECT " +
                        "   specialitiesByIndustry, " +
                        "   afishaEvents " +
                        "FROM SpecialitiesByIndustry specialitiesByIndustry " +
                        "   INNER JOIN AfishaEvents afishaEvents ON " +
                        "       afishaEvents.eventType = :type " +
                        "       AND afishaEvents.eventId = specialitiesByIndustry.eventId " +
                        (!(excludedEventIds.isEmpty()) ? "AND afishaEvents.eventId NOT IN :excludedEventIds " : "") +
                        "       AND afishaEvents.startAt >= :date " +
                        "       AND (afishaEvents.freeCountMember > 0 OR afishaEvents.freeCountMember IS NULL) " +
                        (collegeNames != null || collegeCodes != null ? "AND ( " +
                                (collegeNames != null ? "afishaEvents.collegeName IN :collegeNames " : "1 = 0 ") +
                                "OR " +
                                (collegeCodes != null ? "afishaEvents.collegeCode IN :collegeCodes " : "1 = 0 ") +
                        ") " : "") +
                        "       AND afishaEvents.action NOT IN :deniedAfishaEventActions " +
                        "WHERE 1 = 1 " +
                        "AND specialitiesByIndustry.codeIndustry IN :industryCodes " +
                        (specialityCodes != null ? "AND specialitiesByIndustry.codeSpeciality IN :specialityCodes " : "")
                );

        query.setParameter("type", type);

        if (!(excludedEventIds.isEmpty())) {
            query.setParameter("excludedEventIds", excludedEventIds);
        }

        query.setParameter("industryCodes", ProfessionalTestsAfishaRepositoryUtils.getDefaultEmptyList(industryCodes));

        query.setParameter("date", ProfessionalTestsAfishaRepositoryUtils.getCurrentDateWithDiff());

        if (collegeNames != null) {
            query.setParameter("collegeNames", ProfessionalTestsAfishaRepositoryUtils.getDefaultEmptyList(collegeNames));
        }

        if (collegeCodes != null) {
            query.setParameter("collegeCodes", ProfessionalTestsAfishaRepositoryUtils.getDefaultEmptyList(collegeCodes));
        }

        query.setParameter("deniedAfishaEventActions", Arrays.asList("CANCELED", "NOMOREPLACES"));

        if (specialityCodes != null) {
            query.setParameter("specialityCodes", ProfessionalTestsAfishaRepositoryUtils.getDefaultEmptyList(specialityCodes));
        }

        return ProfessionalTestsAfishaRepositoryUtils.getProforientationRecommendations(((List<?>) query.getResultList()));
    }
}
