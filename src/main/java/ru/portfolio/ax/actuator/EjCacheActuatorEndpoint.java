package ru.portfolio.ax.actuator;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.actuator.endpoint.annotation.DeleteOperation;
import org.springframework.boot.actuator.endpoint.annotation.Endpoint;
import org.springframework.boot.actuator.endpoint.annotation.ReadOperation;
import org.springframework.boot.actuator.endpoint.annotation.Selector;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.stereotype.Component;
import ru.portfolio.ax.configuration.CacheConfig;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Actuator endpoint для управления кешами API ЭЖ
 * Доступен по адресу: /actuator/ej-cache
 */
@Slf4j
@Component
@Endpoint(id = "ej-cache")
@RequiredArgsConstructor
public class EjCacheActuatorEndpoint {

    @Qualifier("ejCacheManager")
    private final CacheManager ejCacheManager;

    /**
     * Получить информацию о всех кешах API ЭЖ
     * GET /actuator/ej-cache
     */
    @ReadOperation
    public Map<String, Object> getCachesInfo() {
        Map<String, Object> result = new HashMap<>();
        
        ejCacheManager.getCacheNames().forEach(cacheName -> {
            Cache cache = ejCacheManager.getCache(cacheName);
            if (cache != null) {
                result.put(cacheName, getCacheDetails(cache));
            }
        });
        
        return result;
    }

    /**
     * Получить информацию о конкретном кеше
     * GET /actuator/ej-cache/{cacheName}
     */
    @ReadOperation
    public Map<String, Object> getCacheInfo(@Selector String cacheName) {
        Cache cache = ejCacheManager.getCache(cacheName);
        if (cache == null) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Cache not found: " + cacheName);
            return error;
        }
        
        return getCacheDetails(cache);
    }

    /**
     * Очистить все кеши API ЭЖ
     * DELETE /actuator/ej-cache
     */
    @DeleteOperation
    public Map<String, Object> clearAllCaches() {
        Map<String, Object> result = new HashMap<>();
        int clearedCount = 0;
        
        for (String cacheName : ejCacheManager.getCacheNames()) {
            Cache cache = ejCacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                clearedCount++;
                log.info("Cleared cache via actuator: {}", cacheName);
            }
        }
        
        result.put("message", "All EJ caches cleared");
        result.put("clearedCaches", clearedCount);
        result.put("cacheNames", ejCacheManager.getCacheNames());
        
        return result;
    }

    /**
     * Очистить конкретный кеш
     * DELETE /actuator/ej-cache/{cacheName}
     */
    @DeleteOperation
    public Map<String, Object> clearCache(@Selector String cacheName) {
        Cache cache = ejCacheManager.getCache(cacheName);
        Map<String, Object> result = new HashMap<>();
        
        if (cache == null) {
            result.put("error", "Cache not found: " + cacheName);
            return result;
        }
        
        cache.clear();
        log.info("Cleared cache via actuator: {}", cacheName);
        
        result.put("message", "Cache cleared: " + cacheName);
        result.put("cacheName", cacheName);
        
        return result;
    }

    /**
     * Очистить конкретный ключ в кеше
     * DELETE /actuator/ej-cache/{cacheName}/{key}
     */
    @DeleteOperation
    public Map<String, Object> evictCacheKey(@Selector String cacheName, @Selector String key) {
        Cache cache = ejCacheManager.getCache(cacheName);
        Map<String, Object> result = new HashMap<>();
        
        if (cache == null) {
            result.put("error", "Cache not found: " + cacheName);
            return result;
        }
        
        cache.evict(key);
        log.info("Evicted key '{}' from cache '{}' via actuator", key, cacheName);
        
        result.put("message", "Key evicted from cache");
        result.put("cacheName", cacheName);
        result.put("key", key);
        
        return result;
    }

    /**
     * Получить детальную информацию о кеше
     */
    private Map<String, Object> getCacheDetails(Cache cache) {
        Map<String, Object> details = new HashMap<>();
        details.put("name", cache.getName());
        details.put("type", cache.getClass().getSimpleName());
        
        // Для Caffeine кеша получаем дополнительную статистику
        if (cache instanceof CaffeineCache) {
            CaffeineCache caffeineCache = (CaffeineCache) cache;
            com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = 
                    caffeineCache.getNativeCache();
            
            details.put("estimatedSize", nativeCache.estimatedSize());
            details.put("stats", nativeCache.stats());
            
            // Дополнительная информация о политике кеша
            Map<String, Object> policy = new HashMap<>();
            policy.put("expireAfterWrite", nativeCache.policy().expireAfterWrite().isPresent());
            policy.put("expireAfterAccess", nativeCache.policy().expireAfterAccess().isPresent());
            policy.put("refreshAfterWrite", nativeCache.policy().refreshAfterWrite().isPresent());
            
            if (nativeCache.policy().expireAfterWrite().isPresent()) {
                policy.put("expireAfterWriteDuration", 
                        nativeCache.policy().expireAfterWrite().get().getExpiresAfter());
            }
            
            if (nativeCache.policy().eviction().isPresent()) {
                policy.put("maximumSize", nativeCache.policy().eviction().get().getMaximum());
            }
            
            details.put("policy", policy);
        } else {
            details.put("nativeCache", cache.getNativeCache().toString());
        }
        
        return details;
    }
}
