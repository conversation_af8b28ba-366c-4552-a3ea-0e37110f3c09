package ru.portfolio.ax.model;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedDate;
import ru.portfolio.ax.model.common.AbstractEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.time.LocalDateTime;

@Data
@Entity
@FieldNameConstants
@EqualsAndHashCode(callSuper = true)
public class UserSetting extends AbstractEntity<Long> {

    private Long userId;
    private String personId;

    @CreatedDate
    @Column(nullable = false, updatable = false, name = "creation_date")
    private LocalDateTime creationDate;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JsonNode setting;

    private Long themeSetting;

    private LocalDateTime systemUpdateDate;

    private String defaultSettingsChecksum;
}
