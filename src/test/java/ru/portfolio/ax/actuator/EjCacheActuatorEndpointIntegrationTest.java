package ru.portfolio.ax.actuator;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import ru.portfolio.ax.configuration.CacheConfig;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
@TestPropertySource(properties = {
        "management.endpoints.web.exposure.include=ej-cache",
        "management.endpoint.ej-cache.enabled=true"
})
class EjCacheActuatorEndpointIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @Test
    void getCachesInfo_ShouldReturnCacheInformation() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        mockMvc.perform(get("/actuator/ej-cache")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isMap())
                .andExpect(jsonPath("$." + CacheConfig.EJ_RANK_CLASS_CACHE).exists())
                .andExpect(jsonPath("$." + CacheConfig.EJ_RANK_SUBJECTS_CACHE).exists());
    }

    @Test
    void getCacheInfo_ShouldReturnSpecificCacheInfo() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        mockMvc.perform(get("/actuator/ej-cache/" + CacheConfig.EJ_RANK_CLASS_CACHE)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name").value(CacheConfig.EJ_RANK_CLASS_CACHE))
                .andExpect(jsonPath("$.type").exists())
                .andExpect(jsonPath("$.estimatedSize").exists())
                .andExpect(jsonPath("$.stats").exists());
    }

    @Test
    void getCacheInfo_ShouldReturnErrorForNonExistentCache() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        mockMvc.perform(get("/actuator/ej-cache/nonexistent")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("Cache not found: nonexistent"));
    }

    @Test
    void clearAllCaches_ShouldClearAllCaches() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        mockMvc.perform(delete("/actuator/ej-cache")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("All EJ caches cleared"))
                .andExpect(jsonPath("$.clearedCaches").value(2))
                .andExpect(jsonPath("$.cacheNames").isArray());
    }

    @Test
    void clearCache_ShouldClearSpecificCache() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        mockMvc.perform(delete("/actuator/ej-cache/" + CacheConfig.EJ_RANK_CLASS_CACHE)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("Cache cleared: " + CacheConfig.EJ_RANK_CLASS_CACHE))
                .andExpect(jsonPath("$.cacheName").value(CacheConfig.EJ_RANK_CLASS_CACHE));
    }

    @Test
    void clearCache_ShouldReturnErrorForNonExistentCache() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        mockMvc.perform(delete("/actuator/ej-cache/nonexistent")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("Cache not found: nonexistent"));
    }

    @Test
    void evictCacheKey_ShouldEvictSpecificKey() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        String testKey = "test-person-123";

        mockMvc.perform(delete("/actuator/ej-cache/" + CacheConfig.EJ_RANK_CLASS_CACHE + "/" + testKey)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("Key evicted from cache"))
                .andExpect(jsonPath("$.cacheName").value(CacheConfig.EJ_RANK_CLASS_CACHE))
                .andExpect(jsonPath("$.key").value(testKey));
    }

    @Test
    void evictCacheKey_ShouldReturnErrorForNonExistentCache() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        mockMvc.perform(delete("/actuator/ej-cache/nonexistent/test-key")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.error").value("Cache not found: nonexistent"));
    }
}
