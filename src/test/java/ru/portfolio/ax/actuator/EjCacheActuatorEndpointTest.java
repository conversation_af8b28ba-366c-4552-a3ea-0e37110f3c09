package ru.portfolio.ax.actuator;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import ru.portfolio.ax.configuration.CacheConfig;

import java.util.Arrays;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EjCacheActuatorEndpointTest {

    @Mock
    private CacheManager ejCacheManager;

    @Mock
    private Cache mockCache;

    @Mock
    private CaffeineCache mockCaffeineCache;

    private EjCacheActuatorEndpoint endpoint;

    @BeforeEach
    void setUp() {
        endpoint = new EjCacheActuatorEndpoint(ejCacheManager);
    }

    @Test
    void getCachesInfo_ShouldReturnAllCaches() {
        // Given
        when(ejCacheManager.getCacheNames()).thenReturn(Arrays.asList(
                CacheConfig.EJ_RANK_CLASS_CACHE, 
                CacheConfig.EJ_RANK_SUBJECTS_CACHE
        ));
        when(ejCacheManager.getCache(CacheConfig.EJ_RANK_CLASS_CACHE)).thenReturn(mockCache);
        when(ejCacheManager.getCache(CacheConfig.EJ_RANK_SUBJECTS_CACHE)).thenReturn(mockCache);
        when(mockCache.getName()).thenReturn(CacheConfig.EJ_RANK_CLASS_CACHE, CacheConfig.EJ_RANK_SUBJECTS_CACHE);
        when(mockCache.getNativeCache()).thenReturn(new Object());

        // When
        Map<String, Object> result = endpoint.getCachesInfo();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey(CacheConfig.EJ_RANK_CLASS_CACHE));
        assertTrue(result.containsKey(CacheConfig.EJ_RANK_SUBJECTS_CACHE));
    }

    @Test
    void getCacheInfo_ShouldReturnCacheDetails() {
        // Given
        String cacheName = CacheConfig.EJ_RANK_CLASS_CACHE;
        when(ejCacheManager.getCache(cacheName)).thenReturn(mockCache);
        when(mockCache.getName()).thenReturn(cacheName);
        when(mockCache.getNativeCache()).thenReturn(new Object());

        // When
        Map<String, Object> result = endpoint.getCacheInfo(cacheName);

        // Then
        assertNotNull(result);
        assertEquals(cacheName, result.get("name"));
        assertNotNull(result.get("type"));
    }

    @Test
    void getCacheInfo_ShouldReturnErrorForNonExistentCache() {
        // Given
        String cacheName = "nonexistent";
        when(ejCacheManager.getCache(cacheName)).thenReturn(null);

        // When
        Map<String, Object> result = endpoint.getCacheInfo(cacheName);

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("error"));
        assertEquals("Cache not found: " + cacheName, result.get("error"));
    }

    @Test
    void clearAllCaches_ShouldClearAllCaches() {
        // Given
        when(ejCacheManager.getCacheNames()).thenReturn(Arrays.asList(
                CacheConfig.EJ_RANK_CLASS_CACHE, 
                CacheConfig.EJ_RANK_SUBJECTS_CACHE
        ));
        when(ejCacheManager.getCache(CacheConfig.EJ_RANK_CLASS_CACHE)).thenReturn(mockCache);
        when(ejCacheManager.getCache(CacheConfig.EJ_RANK_SUBJECTS_CACHE)).thenReturn(mockCache);

        // When
        Map<String, Object> result = endpoint.clearAllCaches();

        // Then
        assertNotNull(result);
        assertEquals("All EJ caches cleared", result.get("message"));
        assertEquals(2, result.get("clearedCaches"));
        
        verify(mockCache, times(2)).clear();
    }

    @Test
    void clearCache_ShouldClearSpecificCache() {
        // Given
        String cacheName = CacheConfig.EJ_RANK_CLASS_CACHE;
        when(ejCacheManager.getCache(cacheName)).thenReturn(mockCache);

        // When
        Map<String, Object> result = endpoint.clearCache(cacheName);

        // Then
        assertNotNull(result);
        assertEquals("Cache cleared: " + cacheName, result.get("message"));
        assertEquals(cacheName, result.get("cacheName"));
        
        verify(mockCache, times(1)).clear();
    }

    @Test
    void clearCache_ShouldReturnErrorForNonExistentCache() {
        // Given
        String cacheName = "nonexistent";
        when(ejCacheManager.getCache(cacheName)).thenReturn(null);

        // When
        Map<String, Object> result = endpoint.clearCache(cacheName);

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("error"));
        assertEquals("Cache not found: " + cacheName, result.get("error"));
    }

    @Test
    void evictCacheKey_ShouldEvictSpecificKey() {
        // Given
        String cacheName = CacheConfig.EJ_RANK_CLASS_CACHE;
        String key = "test-person-123";
        when(ejCacheManager.getCache(cacheName)).thenReturn(mockCache);

        // When
        Map<String, Object> result = endpoint.evictCacheKey(cacheName, key);

        // Then
        assertNotNull(result);
        assertEquals("Key evicted from cache", result.get("message"));
        assertEquals(cacheName, result.get("cacheName"));
        assertEquals(key, result.get("key"));
        
        verify(mockCache, times(1)).evict(key);
    }

    @Test
    void evictCacheKey_ShouldReturnErrorForNonExistentCache() {
        // Given
        String cacheName = "nonexistent";
        String key = "test-key";
        when(ejCacheManager.getCache(cacheName)).thenReturn(null);

        // When
        Map<String, Object> result = endpoint.evictCacheKey(cacheName, key);

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("error"));
        assertEquals("Cache not found: " + cacheName, result.get("error"));
    }
}
