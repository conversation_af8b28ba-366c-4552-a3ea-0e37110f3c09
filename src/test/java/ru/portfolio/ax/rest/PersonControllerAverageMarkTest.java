package ru.portfolio.ax.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import ru.portfolio.ax.rest.dto.AverageMarkResponseDTO;
import ru.portfolio.ax.rest.dto.ej.EjRankClassResponseDTO;
import ru.portfolio.ax.rest.dto.ej.EjRankSubjectsResponseDTO;
import ru.portfolio.ax.service.*;
import ru.portfolio.ax.service.diagnostic.independentRating.IndependentRatingService;
import ru.portfolio.ax.service.ext.DOPService;
import ru.portfolio.ax.service.ext.EjApiService;
import ru.portfolio.ax.service.ext.ProforientationService;
import ru.portfolio.ax.service.giaExam.GiaExamService;
import ru.portfolio.ax.service.gratitudeTeacher.GratitudeTeacherService;
import ru.portfolio.ax.service.proforientation.recommendation.ProforientationRecommendationService;
import ru.portfolio.ax.util.security.AuthComponent;
import ru.portfolio.ax.util.security.AuthComponentNew;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(PersonController.class)
class PersonControllerAverageMarkTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private DataService dataService;

    @MockBean
    private AuthComponent authComponent;

    @MockBean
    private AuthComponentNew authComponentNew;

    @MockBean
    private ShareLinkService shareLinkService;

    @MockBean
    private ClickHouseService clickHouseService;

    @MockBean
    private PrintDocumentService printDocumentService;

    @MockBean
    private DOPService dopService;

    @MockBean
    private SettingService settingService;

    @MockBean
    private RecommendationService recommendationService;

    @MockBean
    private ProforientationRecommendationService proforientationRecommendationService;

    @MockBean
    private ProforientationService proforientationService;

    @MockBean
    private IndependentRatingService independentRatingService;

    @MockBean
    private GratitudeTeacherService gratitudeTeacherService;

    @MockBean
    private GiaExamService giaExamService;

    @MockBean
    private EjApiService ejApiService;

    @Test
    void averageMarkPerformanceV2_ShouldReturnCorrectResponse() throws Exception {
        // Given
        String personId = "test-person-123";
        String authorization = "Bearer test-token";

        // Mock rank class data
        EjRankClassResponseDTO.RankData rankData = EjRankClassResponseDTO.RankData.builder()
                .averageMarkFive(4.0)
                .rankPlace(5)
                .rankStatus("good")
                .build();

        EjRankClassResponseDTO rankClassResponse = EjRankClassResponseDTO.builder()
                .personId(personId)
                .rank(rankData)
                .imageId(123L)
                .build();

        List<EjRankClassResponseDTO> rankClassData = Arrays.asList(rankClassResponse);

        // Mock rank subjects data
        EjRankSubjectsResponseDTO.SubjectRankData physicsRankData = 
                EjRankSubjectsResponseDTO.SubjectRankData.builder()
                        .averageMarkFive(4.0)
                        .rankPlace(3)
                        .rankStatus("good")
                        .build();

        EjRankSubjectsResponseDTO.SubjectRankData chemistryRankData = 
                EjRankSubjectsResponseDTO.SubjectRankData.builder()
                        .averageMarkFive(3.0)
                        .rankPlace(8)
                        .rankStatus("satisfactory")
                        .build();

        EjRankSubjectsResponseDTO physicsResponse = EjRankSubjectsResponseDTO.builder()
                .subjectId(33623584L)
                .subjectName("Физика")
                .rank(physicsRankData)
                .build();

        EjRankSubjectsResponseDTO chemistryResponse = EjRankSubjectsResponseDTO.builder()
                .subjectId(33623585L)
                .subjectName("Химия")
                .rank(chemistryRankData)
                .build();

        List<EjRankSubjectsResponseDTO> rankSubjectsData = Arrays.asList(physicsResponse, chemistryResponse);

        // Mock service calls
        when(ejApiService.getRankClass(eq(personId), any())).thenReturn(rankClassData);
        when(ejApiService.getRankSubjects(eq(personId), any())).thenReturn(rankSubjectsData);

        // When & Then
        mockMvc.perform(get("/persons/{personId}/v2/academic-performance/average-mark", personId)
                        .header("Authorization", authorization)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("OK"))
                .andExpect(jsonPath("$.data.averageMarkFive").value(4.0))
                .andExpect(jsonPath("$.data.subjectsRank").isArray())
                .andExpect(jsonPath("$.data.subjectsRank.length()").value(2))
                .andExpect(jsonPath("$.data.subjectsRank[0].subjectId").value(33623584))
                .andExpect(jsonPath("$.data.subjectsRank[0].subjectName").value("Физика"))
                .andExpect(jsonPath("$.data.subjectsRank[0].averageMarkFiveSubject").value(4.0))
                .andExpect(jsonPath("$.data.subjectsRank[1].subjectId").value(33623585))
                .andExpect(jsonPath("$.data.subjectsRank[1].subjectName").value("Химия"))
                .andExpect(jsonPath("$.data.subjectsRank[1].averageMarkFiveSubject").value(3.0));
    }

    @Test
    void averageMarkPerformanceV2_ShouldHandleEmptyRankClassData() throws Exception {
        // Given
        String personId = "test-person-123";
        String authorization = "Bearer test-token";

        // Mock empty rank class data
        List<EjRankClassResponseDTO> rankClassData = Arrays.asList();

        // Mock rank subjects data
        EjRankSubjectsResponseDTO.SubjectRankData physicsRankData = 
                EjRankSubjectsResponseDTO.SubjectRankData.builder()
                        .averageMarkFive(4.0)
                        .rankPlace(3)
                        .rankStatus("good")
                        .build();

        EjRankSubjectsResponseDTO physicsResponse = EjRankSubjectsResponseDTO.builder()
                .subjectId(33623584L)
                .subjectName("Физика")
                .rank(physicsRankData)
                .build();

        List<EjRankSubjectsResponseDTO> rankSubjectsData = Arrays.asList(physicsResponse);

        // Mock service calls
        when(ejApiService.getRankClass(eq(personId), any())).thenReturn(rankClassData);
        when(ejApiService.getRankSubjects(eq(personId), any())).thenReturn(rankSubjectsData);

        // When & Then
        mockMvc.perform(get("/persons/{personId}/v2/academic-performance/average-mark", personId)
                        .header("Authorization", authorization)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("OK"))
                .andExpect(jsonPath("$.data.averageMarkFive").value(0.0))
                .andExpect(jsonPath("$.data.subjectsRank").isArray())
                .andExpect(jsonPath("$.data.subjectsRank.length()").value(1))
                .andExpect(jsonPath("$.data.subjectsRank[0].subjectId").value(33623584))
                .andExpect(jsonPath("$.data.subjectsRank[0].subjectName").value("Физика"))
                .andExpect(jsonPath("$.data.subjectsRank[0].averageMarkFiveSubject").value(4.0));
    }

    @Test
    void averageMarkPerformanceV2_ShouldHandleEmptySubjectsData() throws Exception {
        // Given
        String personId = "test-person-123";
        String authorization = "Bearer test-token";

        // Mock rank class data
        EjRankClassResponseDTO.RankData rankData = EjRankClassResponseDTO.RankData.builder()
                .averageMarkFive(4.5)
                .rankPlace(2)
                .rankStatus("excellent")
                .build();

        EjRankClassResponseDTO rankClassResponse = EjRankClassResponseDTO.builder()
                .personId(personId)
                .rank(rankData)
                .imageId(123L)
                .build();

        List<EjRankClassResponseDTO> rankClassData = Arrays.asList(rankClassResponse);

        // Mock empty subjects data
        List<EjRankSubjectsResponseDTO> rankSubjectsData = Arrays.asList();

        // Mock service calls
        when(ejApiService.getRankClass(eq(personId), any())).thenReturn(rankClassData);
        when(ejApiService.getRankSubjects(eq(personId), any())).thenReturn(rankSubjectsData);

        // When & Then
        mockMvc.perform(get("/persons/{personId}/v2/academic-performance/average-mark", personId)
                        .header("Authorization", authorization)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("OK"))
                .andExpect(jsonPath("$.data.averageMarkFive").value(4.5))
                .andExpect(jsonPath("$.data.subjectsRank").isArray())
                .andExpect(jsonPath("$.data.subjectsRank.length()").value(0));
    }
}
