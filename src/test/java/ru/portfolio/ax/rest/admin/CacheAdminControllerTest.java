package ru.portfolio.ax.rest.admin;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import ru.portfolio.ax.service.CacheManagementService;
import ru.portfolio.ax.util.security.AuthComponent;
import ru.portfolio.ax.util.security.AuthComponentNew;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(CacheAdminController.class)
class CacheAdminControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private CacheManagementService cacheManagementService;

    @MockBean
    private AuthComponent authComponent;

    @MockBean
    private AuthComponentNew authComponentNew;

    @Test
    void clearAllEjCaches_ShouldReturnSuccess() throws Exception {
        // Given
        doNothing().when(cacheManagementService).evictAllEjCaches();

        // When & Then
        mockMvc.perform(post("/admin/cache/ej/clear-all")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("OK"))
                .andExpect(jsonPath("$.data").value("All EJ caches cleared successfully"));

        verify(cacheManagementService, times(1)).evictAllEjCaches();
        verify(authComponent, times(1)).userAuth(any(), eq("clearAllEjCaches"), eq(null));
    }

    @Test
    void clearPersonCaches_ShouldReturnSuccess() throws Exception {
        // Given
        String personId = "test-person-123";
        doNothing().when(cacheManagementService).evictAllCachesForPerson(personId);

        // When & Then
        mockMvc.perform(post("/admin/cache/ej/clear-person/{personId}", personId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("OK"))
                .andExpect(jsonPath("$.data").value("EJ caches cleared for person: " + personId));

        verify(cacheManagementService, times(1)).evictAllCachesForPerson(personId);
        verify(authComponent, times(1)).userAuth(any(), eq("clearPersonCaches"), eq(null));
    }

    @Test
    void getCacheStats_ShouldReturnStats() throws Exception {
        // Given
        String rankClassStats = "Cache 'ejRankClass': size=10";
        String rankSubjectsStats = "Cache 'ejRankSubjects': size=15";
        
        when(cacheManagementService.getRankClassCacheStats()).thenReturn(rankClassStats);
        when(cacheManagementService.getRankSubjectsCacheStats()).thenReturn(rankSubjectsStats);

        // When & Then
        mockMvc.perform(get("/admin/cache/ej/stats")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("OK"))
                .andExpect(jsonPath("$.data.rankClassCache").value(rankClassStats))
                .andExpect(jsonPath("$.data.rankSubjectsCache").value(rankSubjectsStats));

        verify(cacheManagementService, times(1)).getRankClassCacheStats();
        verify(cacheManagementService, times(1)).getRankSubjectsCacheStats();
        verify(authComponent, times(1)).userAuth(any(), eq("getCacheStats"), eq(null));
    }

    @Test
    void checkPersonCache_ShouldReturnCacheStatus() throws Exception {
        // Given
        String personId = "test-person-123";
        when(cacheManagementService.hasCachedDataForPerson(personId)).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/admin/cache/ej/check-person/{personId}", personId)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("OK"))
                .andExpect(jsonPath("$.data.personId").value(personId))
                .andExpect(jsonPath("$.data.hasCachedData").value(true));

        verify(cacheManagementService, times(1)).hasCachedDataForPerson(personId);
        verify(authComponent, times(1)).userAuth(any(), eq("checkPersonCache"), eq(null));
    }

    @Test
    void clearRankClassCache_ShouldReturnSuccess() throws Exception {
        // Given
        doNothing().when(cacheManagementService).evictAllRankClassCache();

        // When & Then
        mockMvc.perform(post("/admin/cache/ej/clear-rank-class")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("OK"))
                .andExpect(jsonPath("$.data").value("Rank class cache cleared successfully"));

        verify(cacheManagementService, times(1)).evictAllRankClassCache();
        verify(authComponent, times(1)).userAuth(any(), eq("clearRankClassCache"), eq(null));
    }

    @Test
    void clearRankSubjectsCache_ShouldReturnSuccess() throws Exception {
        // Given
        doNothing().when(cacheManagementService).evictAllRankSubjectsCache();

        // When & Then
        mockMvc.perform(post("/admin/cache/ej/clear-rank-subjects")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.result").value("OK"))
                .andExpect(jsonPath("$.data").value("Rank subjects cache cleared successfully"));

        verify(cacheManagementService, times(1)).evictAllRankSubjectsCache();
        verify(authComponent, times(1)).userAuth(any(), eq("clearRankSubjectsCache"), eq(null));
    }
}
