package ru.portfolio.ax.service.ext;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import ru.portfolio.ax.configuration.EjProperty;
import ru.portfolio.ax.rest.dto.ej.EjRankClassResponseDTO;
import ru.portfolio.ax.rest.dto.ej.EjRankSubjectsResponseDTO;

import java.net.URI;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EjApiServiceTest {

    @Mock
    private EjProperty ejProperty;

    @Mock
    private RestTemplate ejRestTemplate;

    private EjApiService ejApiService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        ejApiService = new EjApiService(ejProperty, ejRestTemplate);
    }

    @Test
    void getRankClass_ShouldReturnRankClassData() {
        // Given
        String personId = "test-person-id";
        String authorization = "Bearer test-token";
        String baseUrl = "https://test-api.com/";
        String endpoint = "rating/v1/rank/class";

        when(ejProperty.getUrl()).thenReturn(baseUrl);
        when(ejProperty.getRankClassEndpoint()).thenReturn(endpoint);

        EjRankClassResponseDTO.RankData rankData = EjRankClassResponseDTO.RankData.builder()
                .averageMarkFive(4.5)
                .rankPlace(5)
                .rankStatus("good")
                .build();

        EjRankClassResponseDTO expectedResponse = EjRankClassResponseDTO.builder()
                .personId(personId)
                .rank(rankData)
                .imageId(123L)
                .build();

        List<EjRankClassResponseDTO> expectedList = Arrays.asList(expectedResponse);
        ResponseEntity<List<EjRankClassResponseDTO>> responseEntity = 
                new ResponseEntity<>(expectedList, HttpStatus.OK);

        when(ejRestTemplate.exchange(
                any(URI.class),
                eq(HttpMethod.GET),
                any(),
                any(ParameterizedTypeReference.class)
        )).thenReturn(responseEntity);

        // When
        List<EjRankClassResponseDTO> result = ejApiService.getRankClass(personId, authorization);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(personId, result.get(0).getPersonId());
        assertEquals(4.5, result.get(0).getRank().getAverageMarkFive());
        assertEquals(5, result.get(0).getRank().getRankPlace());
        assertEquals("good", result.get(0).getRank().getRankStatus());
    }

    @Test
    void getRankSubjects_ShouldReturnRankSubjectsData() {
        // Given
        String personId = "test-person-id";
        String authorization = "Bearer test-token";
        String baseUrl = "https://test-api.com/";
        String endpoint = "rating/v1/rank/subjects";

        when(ejProperty.getUrl()).thenReturn(baseUrl);
        when(ejProperty.getRankSubjectsEndpoint()).thenReturn(endpoint);

        EjRankSubjectsResponseDTO.SubjectRankData subjectRankData = 
                EjRankSubjectsResponseDTO.SubjectRankData.builder()
                        .averageMarkFive(4.0)
                        .rankPlace(3)
                        .rankStatus("excellent")
                        .build();

        EjRankSubjectsResponseDTO expectedResponse = EjRankSubjectsResponseDTO.builder()
                .subjectId(33623584L)
                .subjectName("Физика")
                .rank(subjectRankData)
                .build();

        List<EjRankSubjectsResponseDTO> expectedList = Arrays.asList(expectedResponse);
        ResponseEntity<List<EjRankSubjectsResponseDTO>> responseEntity = 
                new ResponseEntity<>(expectedList, HttpStatus.OK);

        when(ejRestTemplate.exchange(
                any(URI.class),
                eq(HttpMethod.GET),
                any(),
                any(ParameterizedTypeReference.class)
        )).thenReturn(responseEntity);

        // When
        List<EjRankSubjectsResponseDTO> result = ejApiService.getRankSubjects(personId, authorization);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(33623584L, result.get(0).getSubjectId());
        assertEquals("Физика", result.get(0).getSubjectName());
        assertEquals(4.0, result.get(0).getRank().getAverageMarkFive());
        assertEquals(3, result.get(0).getRank().getRankPlace());
        assertEquals("excellent", result.get(0).getRank().getRankStatus());
    }

    @Test
    void getRankClass_ShouldHandleEmptyResponse() {
        // Given
        String personId = "test-person-id";
        String authorization = "Bearer test-token";
        String baseUrl = "https://test-api.com/";
        String endpoint = "rating/v1/rank/class";

        when(ejProperty.getUrl()).thenReturn(baseUrl);
        when(ejProperty.getRankClassEndpoint()).thenReturn(endpoint);

        List<EjRankClassResponseDTO> expectedList = Arrays.asList();
        ResponseEntity<List<EjRankClassResponseDTO>> responseEntity = 
                new ResponseEntity<>(expectedList, HttpStatus.OK);

        when(ejRestTemplate.exchange(
                any(URI.class),
                eq(HttpMethod.GET),
                any(),
                any(ParameterizedTypeReference.class)
        )).thenReturn(responseEntity);

        // When
        List<EjRankClassResponseDTO> result = ejApiService.getRankClass(personId, authorization);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getRankSubjects_ShouldHandleEmptyResponse() {
        // Given
        String personId = "test-person-id";
        String authorization = "Bearer test-token";
        String baseUrl = "https://test-api.com/";
        String endpoint = "rating/v1/rank/subjects";

        when(ejProperty.getUrl()).thenReturn(baseUrl);
        when(ejProperty.getRankSubjectsEndpoint()).thenReturn(endpoint);

        List<EjRankSubjectsResponseDTO> expectedList = Arrays.asList();
        ResponseEntity<List<EjRankSubjectsResponseDTO>> responseEntity = 
                new ResponseEntity<>(expectedList, HttpStatus.OK);

        when(ejRestTemplate.exchange(
                any(URI.class),
                eq(HttpMethod.GET),
                any(),
                any(ParameterizedTypeReference.class)
        )).thenReturn(responseEntity);

        // When
        List<EjRankSubjectsResponseDTO> result = ejApiService.getRankSubjects(personId, authorization);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
