package ru.portfolio.ax.service.ext;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cache.CacheManager;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.client.RestTemplate;
import ru.portfolio.ax.configuration.CacheConfiguration;
import ru.portfolio.ax.configuration.EjProperty;
import ru.portfolio.ax.rest.dto.ej.EjRankClassResponseDTO;
import ru.portfolio.ax.rest.dto.ej.EjRankSubjectsResponseDTO;

import java.net.URI;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = {EjApiService.class, CacheConfiguration.class, EjProperty.class})
@TestPropertySource(properties = {
        "ej.api.url=https://test-api.com/",
        "ej.api.rank.class.endpoint=rating/v1/rank/class",
        "ej.api.rank.subjects.endpoint=rating/v1/rank/subjects",
        "ej.api.subsystem=familyweb"
})
class EjApiServiceCacheTest {

    @Autowired
    private EjApiService ejApiService;

    @Autowired
    private CacheManager cacheManager;

    @MockBean
    private RestTemplate ejRestTemplate;

    @BeforeEach
    void setUp() {
        // Очищаем кеши перед каждым тестом
        cacheManager.getCacheNames().forEach(cacheName -> 
                cacheManager.getCache(cacheName).clear());
    }

    @Test
    void getRankClass_ShouldCacheResults() {
        // Given
        String personId = "test-person-123";
        String authorization = "Bearer test-token";

        EjRankClassResponseDTO.RankData rankData = EjRankClassResponseDTO.RankData.builder()
                .averageMarkFive(4.5)
                .rankPlace(5)
                .rankStatus("good")
                .build();

        EjRankClassResponseDTO response = EjRankClassResponseDTO.builder()
                .personId(personId)
                .rank(rankData)
                .imageId(123L)
                .build();

        List<EjRankClassResponseDTO> expectedList = Arrays.asList(response);
        ResponseEntity<List<EjRankClassResponseDTO>> responseEntity = 
                new ResponseEntity<>(expectedList, HttpStatus.OK);

        when(ejRestTemplate.exchange(
                any(URI.class),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenReturn(responseEntity);

        // When - первый вызов
        List<EjRankClassResponseDTO> result1 = ejApiService.getRankClass(personId, authorization);

        // Then - проверяем результат
        assertNotNull(result1);
        assertEquals(1, result1.size());
        assertEquals(personId, result1.get(0).getPersonId());

        // When - второй вызов с теми же параметрами
        List<EjRankClassResponseDTO> result2 = ejApiService.getRankClass(personId, authorization);

        // Then - результат должен быть тот же
        assertEquals(result1, result2);

        // Проверяем, что RestTemplate был вызван только один раз (кеширование работает)
        verify(ejRestTemplate, times(1)).exchange(
                any(URI.class),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        );

        // Проверяем, что данные есть в кеше
        assertNotNull(cacheManager.getCache(CacheConfiguration.EJ_RANK_CLASS_CACHE));
        assertNotNull(cacheManager.getCache(CacheConfiguration.EJ_RANK_CLASS_CACHE).get(personId));
    }

    @Test
    void getRankSubjects_ShouldCacheResults() {
        // Given
        String personId = "test-person-123";
        String authorization = "Bearer test-token";

        EjRankSubjectsResponseDTO.SubjectRankData subjectRankData = 
                EjRankSubjectsResponseDTO.SubjectRankData.builder()
                        .averageMarkFive(4.0)
                        .rankPlace(3)
                        .rankStatus("good")
                        .build();

        EjRankSubjectsResponseDTO response = EjRankSubjectsResponseDTO.builder()
                .subjectId(33623584L)
                .subjectName("Физика")
                .rank(subjectRankData)
                .build();

        List<EjRankSubjectsResponseDTO> expectedList = Arrays.asList(response);
        ResponseEntity<List<EjRankSubjectsResponseDTO>> responseEntity = 
                new ResponseEntity<>(expectedList, HttpStatus.OK);

        when(ejRestTemplate.exchange(
                any(URI.class),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenReturn(responseEntity);

        // When - первый вызов
        List<EjRankSubjectsResponseDTO> result1 = ejApiService.getRankSubjects(personId, authorization);

        // Then - проверяем результат
        assertNotNull(result1);
        assertEquals(1, result1.size());
        assertEquals(33623584L, result1.get(0).getSubjectId());

        // When - второй вызов с теми же параметрами
        List<EjRankSubjectsResponseDTO> result2 = ejApiService.getRankSubjects(personId, authorization);

        // Then - результат должен быть тот же
        assertEquals(result1, result2);

        // Проверяем, что RestTemplate был вызван только один раз (кеширование работает)
        verify(ejRestTemplate, times(1)).exchange(
                any(URI.class),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        );

        // Проверяем, что данные есть в кеше
        assertNotNull(cacheManager.getCache(CacheConfiguration.EJ_RANK_SUBJECTS_CACHE));
        assertNotNull(cacheManager.getCache(CacheConfiguration.EJ_RANK_SUBJECTS_CACHE).get(personId));
    }

    @Test
    void getRankClass_ShouldNotCacheEmptyResults() {
        // Given
        String personId = "test-person-123";
        String authorization = "Bearer test-token";

        List<EjRankClassResponseDTO> emptyList = Arrays.asList();
        ResponseEntity<List<EjRankClassResponseDTO>> responseEntity = 
                new ResponseEntity<>(emptyList, HttpStatus.OK);

        when(ejRestTemplate.exchange(
                any(URI.class),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenReturn(responseEntity);

        // When - первый вызов
        List<EjRankClassResponseDTO> result1 = ejApiService.getRankClass(personId, authorization);

        // When - второй вызов
        List<EjRankClassResponseDTO> result2 = ejApiService.getRankClass(personId, authorization);

        // Then - пустые результаты не должны кешироваться
        assertTrue(result1.isEmpty());
        assertTrue(result2.isEmpty());

        // RestTemplate должен быть вызван дважды (кеширование не работает для пустых результатов)
        verify(ejRestTemplate, times(2)).exchange(
                any(URI.class),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        );

        // Проверяем, что пустые данные не попали в кеш
        assertNull(cacheManager.getCache(CacheConfiguration.EJ_RANK_CLASS_CACHE).get(personId));
    }

    @Test
    void getRankClass_ShouldCacheSeparatelyForDifferentPersons() {
        // Given
        String personId1 = "person-1";
        String personId2 = "person-2";
        String authorization = "Bearer test-token";

        // Настраиваем разные ответы для разных персон
        EjRankClassResponseDTO response1 = EjRankClassResponseDTO.builder()
                .personId(personId1)
                .rank(EjRankClassResponseDTO.RankData.builder().averageMarkFive(4.0).build())
                .build();

        EjRankClassResponseDTO response2 = EjRankClassResponseDTO.builder()
                .personId(personId2)
                .rank(EjRankClassResponseDTO.RankData.builder().averageMarkFive(5.0).build())
                .build();

        when(ejRestTemplate.exchange(
                any(URI.class),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                any(ParameterizedTypeReference.class)
        )).thenReturn(
                new ResponseEntity<>(Arrays.asList(response1), HttpStatus.OK),
                new ResponseEntity<>(Arrays.asList(response2), HttpStatus.OK)
        );

        // When
        List<EjRankClassResponseDTO> result1 = ejApiService.getRankClass(personId1, authorization);
        List<EjRankClassResponseDTO> result2 = ejApiService.getRankClass(personId2, authorization);

        // Then
        assertNotEquals(result1, result2);
        assertEquals(4.0, result1.get(0).getRank().getAverageMarkFive());
        assertEquals(5.0, result2.get(0).getRank().getAverageMarkFive());

        // Проверяем, что оба результата закешированы отдельно
        assertNotNull(cacheManager.getCache(CacheConfiguration.EJ_RANK_CLASS_CACHE).get(personId1));
        assertNotNull(cacheManager.getCache(CacheConfiguration.EJ_RANK_CLASS_CACHE).get(personId2));
    }
}
