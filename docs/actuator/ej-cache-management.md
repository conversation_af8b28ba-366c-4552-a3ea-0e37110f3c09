# Управление кешами API ЭЖ через Spring Boot Actuator

## Обзор

Для управления кешами API ЭЖ доступны как стандартные Actuator endpoints, так и кастомный endpoint `ej-cache`, который предоставляет расширенную функциональность специально для кешей API ЭЖ.

## Конфигурация

В `application.properties` включены следующие endpoints:
```properties
management.endpoints.web.exposure.include=health,prometheus,beans,caches,ej-cache
```

## Стандартные Actuator endpoints

### Получить информацию о всех кешах
```bash
GET /actuator/caches
```

**Ответ:**
```json
{
  "cacheManagers": {
    "ejCacheManager": {
      "caches": {
        "ejRankClass": {
          "target": "com.github.benmanes.caffeine.cache.BoundedLocalCache"
        },
        "ejRankSubjects": {
          "target": "com.github.benmanes.caffeine.cache.BoundedLocalCache"
        }
      }
    }
  }
}
```

### Получить информацию о конкретном кеше
```bash
GET /actuator/caches/ejRankClass
```

### Очистить все кеши
```bash
DELETE /actuator/caches
```

### Очистить конкретный кеш
```bash
DELETE /actuator/caches/ejRankClass
```

## Кастомный endpoint для API ЭЖ

### Получить детальную информацию о кешах API ЭЖ
```bash
GET /actuator/ej-cache
```

**Ответ:**
```json
{
  "ejRankClass": {
    "name": "ejRankClass",
    "type": "CaffeineCache",
    "estimatedSize": 15,
    "stats": {
      "requestCount": 100,
      "hitCount": 85,
      "hitRate": 0.85,
      "missCount": 15,
      "missRate": 0.15,
      "loadCount": 15,
      "loadExceptionCount": 0,
      "totalLoadTime": 1500000000,
      "evictionCount": 0
    },
    "policy": {
      "expireAfterWrite": true,
      "expireAfterWriteDuration": "PT1H",
      "maximumSize": 1000
    }
  },
  "ejRankSubjects": {
    "name": "ejRankSubjects",
    "type": "CaffeineCache",
    "estimatedSize": 20,
    "stats": {
      "requestCount": 80,
      "hitCount": 65,
      "hitRate": 0.8125,
      "missCount": 15,
      "missRate": 0.1875,
      "loadCount": 15,
      "loadExceptionCount": 0,
      "totalLoadTime": 1200000000,
      "evictionCount": 0
    },
    "policy": {
      "expireAfterWrite": true,
      "expireAfterWriteDuration": "PT1H",
      "maximumSize": 1000
    }
  }
}
```

### Получить информацию о конкретном кеше API ЭЖ
```bash
GET /actuator/ej-cache/ejRankClass
```

### Очистить все кеши API ЭЖ
```bash
DELETE /actuator/ej-cache
```

**Ответ:**
```json
{
  "message": "All EJ caches cleared",
  "clearedCaches": 2,
  "cacheNames": ["ejRankClass", "ejRankSubjects"]
}
```

### Очистить конкретный кеш API ЭЖ
```bash
DELETE /actuator/ej-cache/ejRankClass
```

**Ответ:**
```json
{
  "message": "Cache cleared: ejRankClass",
  "cacheName": "ejRankClass"
}
```

### Удалить конкретный ключ из кеша
```bash
DELETE /actuator/ej-cache/ejRankClass/person-123
```

**Ответ:**
```json
{
  "message": "Key evicted from cache",
  "cacheName": "ejRankClass",
  "key": "person-123"
}
```

## Мониторинг кешей

### Ключевые метрики для мониторинга:

1. **Hit Rate** - процент попаданий в кеш (должен быть высоким)
2. **Miss Rate** - процент промахов (должен быть низким)
3. **Estimated Size** - текущий размер кеша
4. **Eviction Count** - количество вытесненных записей
5. **Load Time** - время загрузки данных

### Пример мониторинга через curl:
```bash
#!/bin/bash
# Скрипт для мониторинга кешей API ЭЖ

echo "=== EJ Cache Statistics ==="
curl -s http://localhost:8080/actuator/ej-cache | jq '
  to_entries[] | 
  {
    cache: .key,
    size: .value.estimatedSize,
    hitRate: .value.stats.hitRate,
    missRate: .value.stats.missRate,
    requests: .value.stats.requestCount
  }
'
```

## Автоматизация управления кешем

### Очистка кешей по расписанию (пример):
```bash
#!/bin/bash
# Очистка кешей API ЭЖ каждый день в 2:00
# Добавить в crontab: 0 2 * * * /path/to/clear-ej-cache.sh

curl -X DELETE http://localhost:8080/actuator/ej-cache
echo "EJ caches cleared at $(date)"
```

### Мониторинг и алерты:
```bash
#!/bin/bash
# Проверка hit rate кешей

HIT_RATE_THRESHOLD=0.7

for cache in ejRankClass ejRankSubjects; do
  HIT_RATE=$(curl -s http://localhost:8080/actuator/ej-cache/$cache | jq -r '.stats.hitRate')
  
  if (( $(echo "$HIT_RATE < $HIT_RATE_THRESHOLD" | bc -l) )); then
    echo "WARNING: Cache $cache hit rate is low: $HIT_RATE"
    # Отправить алерт
  fi
done
```

## Безопасность

Actuator endpoints содержат чувствительную информацию и должны быть защищены:

1. **Ограничение доступа**: настроить Spring Security для ограничения доступа к `/actuator/**`
2. **Сетевая изоляция**: endpoints должны быть доступны только из внутренней сети
3. **Аудит**: логировать все операции с кешами

### Пример конфигурации безопасности:
```java
@Configuration
public class ActuatorSecurityConfig {
    
    @Bean
    public SecurityFilterChain actuatorFilterChain(HttpSecurity http) throws Exception {
        return http
            .requestMatcher(EndpointRequest.toAnyEndpoint())
            .authorizeHttpRequests(auth -> auth
                .requestMatchers(EndpointRequest.to("health")).permitAll()
                .anyRequest().hasRole("ADMIN")
            )
            .build();
    }
}
```

## Troubleshooting

### Проблема: Endpoint не доступен
**Решение:** Проверить конфигурацию в `application.properties`:
```properties
management.endpoints.web.exposure.include=ej-cache
management.endpoint.ej-cache.enabled=true
```

### Проблема: Низкий hit rate
**Решение:** 
1. Увеличить TTL кеша
2. Проверить паттерны использования API
3. Увеличить размер кеша

### Проблема: Высокое потребление памяти
**Решение:**
1. Уменьшить максимальный размер кеша
2. Уменьшить TTL
3. Очистить кеши вручную
