# API метод получения средней оценки и рейтинга по предметам v2

## Описание
Метод `GET /persons/{personId}/v2/academic-performance/average-mark` возвращает данные о средней оценке учащегося и его рейтинге по предметам, полученные из API ЭЖ (Электронный журнал).

## URL
```
GET /persons/{personId}/v2/academic-performance/average-mark
```

## Параметры

### Path параметры
- `personId` (string, required) - ID учащегося

### Headers
- `Authorization` (string, optional) - Bearer токен для авторизации
- `Cookie: SHARE` (string, optional) - Cookie для shared ссылок

## Ответ

### Успешный ответ (200 OK)
```json
{
    "result": "OK",
    "data": {
        "averageMarkFive": 4.0,
        "subjectsRank": [
            {
                "subjectId": 33623584,
                "subjectName": "Физика",
                "averageMarkFiveSubject": 4.0
            },
            {
                "subjectId": 33623585,
                "subjectName": "Химия",
                "averageMarkFiveSubject": 3.0
            }
        ]
    }
}
```

### Поля ответа
- `averageMarkFive` (number) - Общая средняя оценка по пятибалльной системе
- `subjectsRank` (array) - Массив данных по предметам
  - `subjectId` (number) - ID предмета
  - `subjectName` (string) - Название предмета
  - `averageMarkFiveSubject` (number) - Средняя оценка по предмету по пятибалльной системе

## Источники данных

Метод получает данные из двух endpoints API ЭЖ:

1. **Общая средняя оценка**: `GET ej/rating/v1/rank/class`
   - Параметры: `personId`, `date` (текущая дата)
   - Заголовки: `Authorization`, `x-mes-subsystem: familyweb`

2. **Рейтинг по предметам**: `GET ej/rating/v1/rank/subjects`
   - Параметры: `personId`, `date` (текущая дата)
   - Заголовки: `Authorization`, `x-mes-subsystem: familyweb`

## Конфигурация

### application.properties
```properties
# URL API ЭЖ
ej.api.url=${EJ_URL:https://school-dev.mos.ru/api/ej/}
ej.api.rank.class.endpoint=rating/v1/rank/class
ej.api.rank.subjects.endpoint=rating/v1/rank/subjects

# Подсистема для заголовка x-mes-subsystem
ej.api.subsystem=${EJ_SUBSYSTEM:familyweb}
```

## Авторизация
Метод требует авторизации с ролями:
- STUDENT
- AGENT (родитель)
- ADMIN

## Обработка ошибок

### Возможные ошибки от API ЭЖ:
- `401` - Unauthorized (неверный токен)
- `403` - Forbidden (недостаточно прав)
- `404` - Not Found (данные не найдены)
- `500` - Internal Server Error (ошибка сервера ЭЖ)

### Обработка отсутствующих данных:
- Если нет данных о рейтинге класса, `averageMarkFive` = 0.0
- Если нет данных по предметам, `subjectsRank` = []

## Примеры использования

### cURL запрос
```bash
curl -X GET \
  'https://portfolio.mos.ru/app/persons/8d3d97a2-0ada-43fc-ac23-d40ae94e8de1/v2/academic-performance/average-mark' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json'
```

### JavaScript (fetch)
```javascript
const response = await fetch('/persons/8d3d97a2-0ada-43fc-ac23-d40ae94e8de1/v2/academic-performance/average-mark', {
    method: 'GET',
    headers: {
        'Authorization': 'Bearer YOUR_TOKEN',
        'Content-Type': 'application/json'
    }
});

const data = await response.json();
console.log('Средняя оценка:', data.data.averageMarkFive);
console.log('Предметы:', data.data.subjectsRank);
```

## Компоненты реализации

### Основные классы:
- `PersonController.averageMarkPerformanceV2()` - контроллер метода
- `EjApiService` - сервис для работы с API ЭЖ
- `AverageMarkResponseDTO` - DTO для ответа
- `EjRankClassResponseDTO` - DTO для данных рейтинга класса
- `EjRankSubjectsResponseDTO` - DTO для данных рейтинга по предметам

### Конфигурация:
- `EjProperty` - конфигурация API ЭЖ
- `ejRestTemplate` - RestTemplate для вызовов API ЭЖ
- `EjServiceErrorHandler` - обработчик ошибок API ЭЖ

## Тестирование
- Unit тесты: `EjApiServiceTest`
- Integration тесты: `PersonControllerAverageMarkTest`
